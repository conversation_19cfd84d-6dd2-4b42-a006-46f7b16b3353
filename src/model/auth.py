#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import StrEnum
from datetime import datetime

from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column

from model.base import BaseModel, g_attr


class RoleType(StrEnum):
    super_admin = "super_admin"
    tenant_admin = "tenant_admin"
    tenant_user = "tenant_user"


class RoleName(StrEnum):
    super_admin = "超级管理员"
    tenant_admin = "租户管理员"
    tenant_user = "租户用户"


class RoleModel(BaseModel):
    __tablename__ = "role"
    __comment__ = "角色表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="角色ID")
    tenant_id: Mapped[int] = mapped_column(nullable=False, default=g_attr("tenant_id"), comment="租户ID")
    name: Mapped[str] = mapped_column(String(12), nullable=False, comment="角色名称")
    type_: Mapped[str] = mapped_column(String(16), nullable=False, comment="角色类型")
    # permissions: Mapped[list] = mapped_column(nullable=False, comment="权限列表")


class UserModel(BaseModel):
    __tablename__ = "user"
    __comment__ = "用户表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="用户ID")
    tenant_id: Mapped[int] = mapped_column(nullable=False, default=g_attr("tenant_id"), comment="租户ID")
    username: Mapped[str] = mapped_column(String(32), nullable=False, comment="用户账号")
    password: Mapped[str] = mapped_column(String(256), nullable=False, comment="用户密码")
    nickname: Mapped[str] = mapped_column(String(12), nullable=False, comment="用户昵称")
    email: Mapped[str] = mapped_column(String(32), nullable=True, comment="用户邮箱")
    phone_number: Mapped[str] = mapped_column(String(16), nullable=True, comment="用户手机")
    expire_time: Mapped[datetime] = mapped_column(nullable=True, comment="到期时间")
    role_ids: Mapped[list] = mapped_column(nullable=False, comment="角色ID")
    info: Mapped[dict] = mapped_column(nullable=False, default='{}', comment="其他信息")


class TenantModel(BaseModel):
    __tablename__ = "tenant"
    __comment__ = "租户表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="租户ID")
    name: Mapped[str] = mapped_column(String(12), nullable=False, comment="租户名称")
