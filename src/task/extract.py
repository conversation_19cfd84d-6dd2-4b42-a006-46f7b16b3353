#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
from collections import Counter

import jieba.posseg as psg

from config import LLMModel
from common import g
from common.logger import logger
from engine.es import es
from controller.operator.workflow.doc_extract import DocExtractWorkflow, DocModel as LLMDocModel, DocExtractWorkflowModel
from controller.operator.runner.base import RunnerStatus
from controller.repository import Repo, Doc, DocStatus, get_index
from controller.stats import TokenCounts, LLMBusiness
from controller.system_config import ModelInfo
from exception import NotFoundError


class DocExtractBase:
    keyword_filter_pattern = re.compile(r"^(?:\d+|[0-9\W]+|[a-zA-Z]{1,2}|[a-zA-Z]{1,2}\W*)$")

    def __init__(self, doc_id: int):
        assert doc_id is not None, "抽取初始化必须传入doc_id"

        self.doc_id = doc_id
        self.extract_model: str | None = None
        self.log_prefix = f"Doc[{doc_id}]抽取 "
        self.error: Exception | None = None

        self.doc: dict | None = None
        self.tenant_id: int | None = None
        self.extract_result: dict | None = None

    async def exec(self):
        should_exec = await self.preparing()
        if not should_exec:
            logger.info(f"{self.log_prefix}任务退出")
            return
        try:
            await self.extract()
            await self.data_processing()
        except Exception as err:
            logger.exception(err)
            logger.error(f"{self.log_prefix}任务失败")
            await self.data_processing()
        else:
            logger.info(f"{self.log_prefix}任务成功")

    async def check_exec(self) -> bool:
        await TokenCounts.check_plan(tenant_id=self.tenant_id)

        repo = await Repo.get_one(repo_id=self.doc["repo_id"])
        if not repo:
            raise NotFoundError("未找到目标知识库")

        if repo["extract_model_id"]:
            model_info = await ModelInfo.get_one(model_id=repo["extract_model_id"])
            if not model_info:
                raise NotFoundError("未找到目标模型")
            self.extract_model = model_info["model_name"]
            return True
        else:
            logger.info(f"{self.log_prefix}知识库未开启抽取")
            return False

    async def extract(self):
        await Doc.update(doc_id=self.doc_id, status=DocStatus.extracting)
        await g.session.commit()

        index = get_index([self.doc["repo_id"]])

        source = await es.get_source(index=index, id=self.doc_id, source_includes=["title", "plain_text", "data_time"])

        llm_doc = LLMDocModel(title=source["title"], content=source["plain_text"], data_time=source["data_time"][:10])
        # noinspection PyArgumentList
        runner = DocExtractWorkflow(
            doc=llm_doc,
            model=DocExtractWorkflowModel(
                BaseExtractModel=self.extract_model,
                SentimentAnalysisModel=self.extract_model,
                CustomExtractModel=self.extract_model,
                CombineAbstractModel=self.extract_model)
        )
        self.extract_result = await runner.run()
        if runner.status == RunnerStatus.FAILED:
            raise RuntimeError(f"抽取失败: {runner.error_msg}")

        # 获取和清洗thinking_content
        thinking_content= []
        for thinking_chunk in runner.thinking_content:
            for item in thinking_chunk["thinking_content_list"]:
                # 去除markdown格式
                item["content"] =  re.sub(
                    pattern=r"```markdown(.*?)```",
                    repl=lambda m: m.group(1).strip(),
                    string=item["thinking_content"],
                    flags=re.DOTALL)
                thinking_content.append(item)
        self.extract_result["think_content"] = thinking_content

        await TokenCounts.record(
            model_name=self.extract_model,
            business=LLMBusiness.doc_extract,
            input_tokens=sum(runner.token_consumption.input_token),
            output_tokens=sum(runner.token_consumption.output_token),
            create_user_id=self.doc["create_user_id"],
            tenant_id=self.tenant_id)
        await g.session.commit()

        if isinstance(self.extract_result, (list, dict)):
            self.extract_result["positive_view_keywords"] = await self._extract_view_keywords(
                views=self.extract_result["positive_view"])
            self.extract_result["negative_view_keywords"] = await self._extract_view_keywords(
                views=self.extract_result["negative_view"])
        else:
            if isinstance(self.extract_result, Exception):
                raise self.extract_result
            logger.error(self.log_prefix+str(self.extract_result))

    async def preparing(self):
        await self.load_doc()
        should_exec = await self.check_exec()
        logger.info(f"{self.log_prefix}preparing阶段完成")
        return should_exec

    async def load_doc(self):
        doc = await Doc.get_one(doc_id=self.doc_id)
        if not doc:
            raise NotFoundError("未找到目标文档")
        self.doc = doc
        self.tenant_id = doc["tenant_id"]

    async def data_processing(self):
        await self.es_data_index()
        await self.rdb_data_update()
        logger.info(f"{self.log_prefix}data_processing阶段完成")

    async def es_data_index(self):
        await es.update(
            index=get_index([self.doc["repo_id"]]),
            id=self.doc_id,
            body={
                "doc": {
                    "extract_result": self.extract_result if self.extract_result else {},
                    "status": DocStatus.extract_success if self.error is None else DocStatus.extract_fail,
                }
            },
            refresh=True,
        )

    async def rdb_data_update(self):
        logger.info(f"{self.log_prefix}执行RDB doc表更新")
        await Doc.update(
            doc_id=self.doc_id,
            status=DocStatus.extract_success if self.error is None else DocStatus.extract_fail)
        await g.session.commit()

    async def _extract_view_keywords(self, views: list, topn: int = 6):
        keywords = []

        if not views:
            return keywords

        view = " ".join(views)
        tokens = []
        step = 10000

        for batch_start in range(0, len(view), step):
            batch_text = view[batch_start:batch_start + step]
            response = await es.indices.analyze(
                index=get_index([self.doc["repo_id"]]),
                body={
                    "analyzer": "ik_smart",
                    "text": batch_text,
                }
            )
            tokens.extend(
                [t["token"] for t in response["tokens"] if len(t["token"]) > 1 and not t["token"].isnumeric()])

        counter_words = [item[0] for item in
                         sorted(Counter(tokens).items(), key=lambda x: x[1], reverse=True)[:500]]
        for pair in psg.lcut(" ".join(counter_words)):
            word = pair.word.strip()
            if (len(word) > 1
                    and ("n" in pair.flag)
                    and word not in keywords
                    and (not self.keyword_filter_pattern.fullmatch(word))):
                keywords.append(word)
                if len(keywords) >= topn:
                    break

        keywords = keywords
        return keywords


class DocExtractOfflineTask:
    @staticmethod
    async def extract(doc_id: int):
        extractor = DocExtractBase(doc_id=doc_id)
        await extractor.exec()


DocExtractOffline = DocExtractOfflineTask()
