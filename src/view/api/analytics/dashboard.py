#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query, Body
from pydantic import Field

from engine.rdb import g
from view import BaseView, api_description
from controller.analytics import Dashboard, EmailDelivery, Metric


class DashboardListView(BaseView):
    @api_description(summary="查询分析面板列表")
    async def get(self,
                  match: Annotated[str, Query(), Field(title="匹配字符")] = None,
                  page: Annotated[int, Query(), Field(title="页码")] = 1,
                  per_page: Annotated[int, Query(), Field(title="每页数量")] = 20,
                  order_by: Annotated[str, Query(), Field(title="排序字段")] = "create_time:desc"):
        pager, dashboard = await Dashboard.get_list(match=match, page=page, per_page=per_page, order_by=order_by)

        return self.response(data=dashboard, pager=pager)


class DashboardMetricsPartView(BaseView):
    @api_description(summary="查询分析面板-指标")
    async def get(self,
                  dashboard_id: Annotated[str, Query(), Field(title="分析面板ID")]):
        metrics = await Dashboard.get_metric_all(dashboard_id=dashboard_id)

        return self.response(data=metrics)

    @api_description(summary="删除分析指标")
    async def delete(self,
                     dashboard_id: Annotated[int, Query(), Field(title="分析面板名称")]):
        await Dashboard.delete_metrics(dashboard_id=dashboard_id)
        return self.response(message="删除成功")


class DashboardView(BaseView):
    @api_description(summary="查询分析面板")
    async def get(self,
                  dashboard_id: Annotated[str, Query(), Field(title="分析面板ID")]):
        dashboard = await Dashboard.get_one(dashboard_id=dashboard_id)

        return self.response(data=dashboard)

    @api_description(summary="创建分析面板")
    async def post(self,
                   name: Annotated[str, Body(), Field(title="分析面板名称", min_length=1, max_length=12)],
                   execution: Annotated[str, Body(), Field(title="数据更新时间", min_length=1, max_length=12)],
                   focus: Annotated[str, Body(), Field(title="分析目标", min_length=1, max_length=50)],
                   combin_summarizers: Annotated[list[list[int]], Body(), Field(title="组合摘要分析IDs")],
                   metrics: Annotated[list[Metric], Body(), Field(title="指标信息")],
                   add_info: Annotated[str, Body(), Field(title="补充信息", min_length=0, max_length=200)] = "",
                   sup_summarizers: Annotated[list[list[int]], Body(), Field(title="补充摘要分析IDs")] = None):
        Dashboard.check(combin_summarizers=combin_summarizers, sup_summarizers=sup_summarizers)
        dashboard_id = await Dashboard.create(
            name=name, execution=execution, focus=focus, combin_summarizers=combin_summarizers,
            metrics=[m.model_dump() for m in metrics], add_info=add_info, sup_summarizers=sup_summarizers)

        await EmailDelivery.create(dashboard_id=dashboard_id, address=[], element=[])
        await Dashboard.send_crontab_task(dashboard_id=dashboard_id, crontab=execution)

        await g.session.commit()

        return self.response(data={"dashboard_id": dashboard_id})

    @api_description(summary="修改分析面板")
    async def put(self,
                  dashboard_id: Annotated[int, Body(), Field(title="分析面板名称")],
                  name: Annotated[str, Body(), Field(title="分析面板名称", min_length=1, max_length=12)] = None,
                  execution: Annotated[str, Body(), Field(title="数据更新时间", min_length=1, max_length=12)] = None,
                  focus: Annotated[str, Body(), Field(title="分析目标", min_length=1, max_length=50)] = None,
                  combin_summarizers: Annotated[list[list[int]], Body(), Field(title="组合摘要分析IDs")] = None,
                  metrics: Annotated[list[Metric], Body(), Field(title="指标信息")] = None,
                  sup_summarizers: Annotated[list[list[int]], Body(), Field(title="补充摘要分析IDs")] = None,
                  add_info: Annotated[str, Body(), Field(title="补充信息", min_length=0, max_length=200)] = ""):
        Dashboard.check(combin_summarizers=combin_summarizers, sup_summarizers=sup_summarizers)
        await Dashboard.update(
            dashboard_id=dashboard_id, name=name, execution=execution, focus=focus,
            combin_summarizers=combin_summarizers, metrics=[m.model_dump() for m in metrics], add_info=add_info,
            sup_summarizers=sup_summarizers)
        await Dashboard.delete_crontab_task(dashboard_id=dashboard_id)
        await Dashboard.send_crontab_task(dashboard_id=dashboard_id, crontab=execution)

        await g.session.commit()

        return self.response(message="修改成功")

    @api_description(summary="删除分析面板")
    async def delete(self,
                     dashboard_id: Annotated[int, Query(), Field(title="分析面板ID")]):
        await Dashboard.delete(dashboard_id=dashboard_id)

        await g.session.commit()

        return self.response(message="删除成功")
