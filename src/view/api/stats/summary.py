#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query
from pydantic import Field

from view import BaseView, api_description
from controller.stats import Stats, StatsContent
from controller.repository import <PERSON><PERSON>, Doc
from controller.analytics import SummarizerTask, SummarizerTaskStatus
from controller.stats.token_counts import TokenCounts
from controller.admin.tenant import Tenant
from common.time import now_datetime, time_delta_translate


class StatsSummaryView(BaseView):
    @api_description(summary="首页数据统计")
    async def get(self,
                  period: Annotated[str, Query(), Field(title="周期")] = "1d"):
        end_time = now_datetime()
        start_time = end_time - time_delta_translate(period=period)

        start = start_time.strftime("%Y-%m-%d %H:%M:%S")
        end = end_time.strftime("%Y-%m-%d %H:%M:%S")

        # ES文档统计
        stats_contents = [
            StatsContent.last_docs,
            StatsContent.sentiment_score_range,
            StatsContent.top_sentiment_docs,
            StatsContent.top_sentiment_tags,
            StatsContent.top_subject,
            StatsContent.top_doc_keywords,
            StatsContent.top_view_keywords,
            StatsContent.view_count_ratio_trend
        ]
        stats_result = await Stats.get_doc_stats(stats_contents=stats_contents, start=start, end=end)

        # 摘要分析统计
        _, summarizer_tasks = await SummarizerTask.get_list(
            status=SummarizerTaskStatus.succeeded, per_page=5, order_by="create_time:desc", start=start, end=end)
        stats_result["last_summarizer_task"] = [{k: v for k, v in sk.items() if k in ("summarizer_name", "summarizer_task_id", "summarizer_id", "update_time")}
                                                for sk in summarizer_tasks]

        # 知识库文档量统计
        repo_doc_counts = await Doc.stats_repo_doc_counts()
        repo_id_name_mapping = await Repo.get_id_name_mapping(repo_ids=[row["repo_id"] for row in repo_doc_counts])
        stats_result["repo_doc_count"] = [{
            "name": repo_id_name_mapping[row["repo_id"]],
            "value": row["doc_count"]}
            for row in repo_doc_counts if row["repo_id"] in repo_id_name_mapping]

        # tokens统计
        token_stats = await TokenCounts.get_one(start=start, end=end)
        stats_result.update(token_stats)

        # 租户套餐token统计
        token_limit_used = await Tenant.get_plan_activate_stats()
        stats_result.update(token_limit_used)

        # 租户使用token分布统计
        token_used_stats = await TokenCounts.get_business_stats(start=start, end=end)
        stats_result["business_stats"] = token_used_stats

        return self.response(data=stats_result)
