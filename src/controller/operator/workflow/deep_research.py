import asyncio
import json
import logging
from datetime import datetime
from enum import StrEnum
from typing import AsyncIterable, Sequence, Optional, Annotated, List, Any

from autogen_agentchat.base import Response
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.messages import TextMessage, BaseChatMessage, ToolCallExecutionEvent, ToolCallRequestEvent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core import CancellationToken
from pydantic import BaseModel, Field

from common.logger import logger
from controller.engine import deepseek_chat, deepseek_reasoner, gemini_2_5_pro
from controller.operator.agent.deep_research.coordinator import CoordinatorAgent
from controller.operator.agent.deep_research.planner import PlannerAgent, StructurePlanAgent, ResearchPlan
from controller.operator.agent.deep_research.researcher import ResearchAgent, ResearchSummaryAgent, \
    ResearcherReflectAgent
from controller.operator.agent.deep_research.summarizer import SummaryAgent, ReflectAgent, IterateAgent
from controller.operator.agent.deep_research.reporter import SubReporterAgent, ReporterAgent
from controller.operator.workflow import BaseWorkflow


class DeepResearchStage(StrEnum):
    ANSWER = "answer"
    PLANNING = "planning"
    THINKING = "thinking"
    REPORTING = "reporting"
    START_REPORTING = "start_reporting"
    RESEARCHING = "researching"
    ERROR = "error"
    STOP = "stop"


class DeepResearchResponse(BaseModel):
    stage: Annotated[DeepResearchStage, Field(title="思考阶段")]
    create_time: Annotated[datetime, Field(title="创建时间", default_factory=datetime.now)] = None
    title: Annotated[Optional[str], Field(title="标题")] = None
    content: Annotated[Optional[str], Field(title="回复内容")] = None
    reference: Annotated[Optional[Any], Field(title="参考文献列表")] = None
    error_msg: Annotated[Optional[str], Field(title="错误信息")] = None


class DeepResearchPlanWorkflow(BaseWorkflow):
    def __init__(self, user_prompt: str, use_web_search: bool = True):
        super().__init__()

        self.user_prompt = user_prompt
        self.use_web_search = use_web_search

    async def _run(self) -> AsyncIterable:
        coordinator_agent = CoordinatorAgent(model_client=deepseek_chat)
        messages: Sequence[BaseChatMessage] = [TextMessage(source="user", content=self.user_prompt)]
        cancellation_token = CancellationToken()
        use_tool, response = False, None
        async for message in coordinator_agent.on_messages_stream(messages, cancellation_token):
            if isinstance(message, ToolCallExecutionEvent):
                use_tool = True
            elif isinstance(message, Response):
                response = message

        if not use_tool:
            logger.info("No tool used, returning response directly.")
            yield DeepResearchResponse(stage=DeepResearchStage.ANSWER, content=response.chat_message.content)
            return

        planner_agent = PlannerAgent(model_client=gemini_2_5_pro)
        messages: Sequence[BaseChatMessage] = [TextMessage(source="user", content=self.user_prompt)]
        cancellation_token = CancellationToken()

        async for message in planner_agent.on_messages_stream(messages, cancellation_token):
            if isinstance(message, Response):
                response = message

        plan = self.get_markdown_content(response.chat_message.content)
        logger.info(plan)
        yield DeepResearchResponse(stage=DeepResearchStage.PLANNING, content=plan)


class DeepResearchSearchWorkflow(BaseWorkflow):
    def __init__(self, user_prompt: str, plan: str, use_web_search: bool = True):
        super().__init__()

        self.user_prompt = user_prompt
        self.plan = plan
        self.use_web_search = use_web_search
        self.stop_flag = False

    async def _run(self) -> AsyncIterable:
        structure_plan_agent = StructurePlanAgent(model_client=deepseek_chat)
        messages: Sequence[BaseChatMessage] = [TextMessage(source="user", content=self.plan)]
        cancellation_token = CancellationToken()

        response = await structure_plan_agent.on_messages(messages, cancellation_token)
        plan_json = self.get_json_content(response.chat_message.content)
        plan = ResearchPlan.model_validate(json.loads(plan_json))
        last_report = ""

        report_list = []
        tavily_search_results, retriever_search_results = [], []

        for sub_plan in plan.plan:
            if self.stop_flag:
                break
            logger.info(f"Processing sub-plan: {sub_plan.description}")

            if last_report:
                content = f"上次报告内容：{last_report}\n\n当前计划：{sub_plan.description}"
            else:
                content = f"当前计划：{sub_plan.description}"

            summary_agent = SummaryAgent()
            messages: List[BaseChatMessage] = [
                TextMessage(source="user", content=content),
            ]

            response = await summary_agent.on_messages(messages, cancellation_token)
            res = response.chat_message.content
            yield DeepResearchResponse(stage=DeepResearchStage.THINKING, content=res.content, title=res.title)

            reflect_agent = ReflectAgent()
            messages.append(TextMessage(source="assistant", content=response.chat_message.content.content))

            response = await reflect_agent.on_messages(messages, cancellation_token)
            res = response.chat_message.content
            yield DeepResearchResponse(stage=DeepResearchStage.THINKING, content=res.content, title=res.title)

            iterate_agent = IterateAgent()
            messages.append(TextMessage(source="assistant", content=response.chat_message.content.content))

            response = await iterate_agent.on_messages(messages, cancellation_token)
            res = response.chat_message.content
            yield DeepResearchResponse(stage=DeepResearchStage.THINKING, content=res.content, title=res.title)

            new_plan = res.content

            researcher_agent = ResearchAgent(sub_plan=new_plan, user=self.user_prompt)
            researcher_summary_agent = ResearchSummaryAgent(sub_plan=new_plan, user=self.user_prompt)
            researcher_reflect_agent = ResearcherReflectAgent(sub_plan=new_plan, user=self.user_prompt)

            text_termination = TextMentionTermination("APPROVE")

            team = RoundRobinGroupChat(
                participants=[researcher_agent, researcher_summary_agent, researcher_reflect_agent],
                termination_condition=text_termination,
                max_turns=18
            )

            tavily_search_sub_results, retriever_search_sub_results = [], []

            for i in range(3):
                try:
                    async for message in team.run_stream(task=new_plan):
                        if self.stop_flag:
                            break

                        if isinstance(message, TextMessage):
                            logger.info("-- content message --")
                            logger.info(f"{message.source}: {message.content}")

                            if message.source == "research_summary":
                                last_report = message.content

                        elif isinstance(message, ToolCallRequestEvent):
                            logger.info("-- tool call request message --")
                            logger.info(f"{message.content[0].name}: {message.content[0].arguments}")

                        elif isinstance(message, ToolCallExecutionEvent):
                            logger.info("-- tool call execution message --")
                            logger.info(f"{message.content[0].name}: {json.loads(message.content[0].content)}")

                            if message.content[0].name == "tavily_search":
                                tavily_search_sub_results.extend(json.loads(message.content[0].content))
                                yield DeepResearchResponse(
                                    stage=DeepResearchStage.RESEARCHING,
                                    reference=json.loads(message.content[0].content),
                                    title="tavily_search"
                                )

                            elif message.content[0].name == "retriever_search":
                                retriever_search_sub_results.extend(json.loads(message.content[0].content))
                                yield DeepResearchResponse(
                                    stage=DeepResearchStage.RESEARCHING,
                                    reference=json.loads(message.content[0].content),
                                    title="retriever_search"
                                )

                    break
                except Exception as e:
                    logging.error(f"Error when running teams: {e}")

            report_list.append(last_report)
            tavily_search_results.append(tavily_search_sub_results)
            retriever_search_results.append(retriever_search_sub_results)

        yield DeepResearchResponse(stage=DeepResearchStage.START_REPORTING)

        async def make_sub_report(sub_plan, research_report, results):
            sub_report_agent = SubReporterAgent(sub_plan=sub_plan, research_report=research_report)
            messages: Sequence[BaseChatMessage] = [
                TextMessage(source="user", content=json.dumps(results, ensure_ascii=False)),
            ]

            response = await sub_report_agent.on_messages(messages, cancellation_token=cancellation_token)
            return response.chat_message.content

        tasks = []
        for i, sub_report in enumerate(report_list):
            results = retriever_search_results[i] + tavily_search_results[i]
            tasks.append(make_sub_report(sub_plan=plan.plan[i], research_report=sub_report, results=results))

        sub_reports = await asyncio.gather(*tasks, return_exceptions=False)

        reporter_agent = ReporterAgent(
            user=self.user_prompt,
            list_of_sub_reports=sub_reports
        )
        messages: Sequence[BaseChatMessage] = [
            TextMessage(source="user", content="现在，请以首席分析师的身份，开始执行你的综合分析任务。"),
        ]
        cancellation_token = CancellationToken()

        response = await reporter_agent.on_messages(messages, cancellation_token=cancellation_token)
        yield DeepResearchResponse(stage=DeepResearchStage.REPORTING, content=response.chat_message.content)


if __name__ == '__main__':
    async def plan_pipeline():
        plan_workflow = DeepResearchPlanWorkflow(user_prompt="特斯拉的发展历程和未来展望")
        async for response in plan_workflow.run_stream():
            print(response)

    async def search_pipeline():
        plan = """
(1) 梳理特斯拉发展历程的关键阶段：检索公司年报、创始人传记，提取关键里程碑。
"""
        search_workflow = DeepResearchSearchWorkflow(user_prompt="特斯拉的发展历程和未来展望", plan=plan)
        async for response in search_workflow.run_stream():
            print(response)

    asyncio.run(search_pipeline())

