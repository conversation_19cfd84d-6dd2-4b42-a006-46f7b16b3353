from typing import Annotated

from autogen_core.models import ChatCompletionClient
from pydantic import BaseModel, Field

from controller.engine import gemini_2_5_flash, gpt_41
from controller.operator.agent import BaseAgent
from controller.operator.runner.base import TokenConsumption
from controller.operator.prompt.deep_research.summarizer import summary_prompt, reflect_prompt, iterate_prompt


class SummaryResponse(BaseModel):
    title: Annotated[str, Field(title="标题")]
    content: Annotated[str, Field(title="内容")]


class SummaryAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = gemini_2_5_flash, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="summary",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=summary_prompt,
            reflect_on_tool_use=True,
            output_content_type=SummaryResponse
        )

class ReflectAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = gemini_2_5_flash, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="reflect",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=reflect_prompt,
            reflect_on_tool_use=True,
            output_content_type=SummaryResponse
        )

class IterateAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = gemini_2_5_flash, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="iterate",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=iterate_prompt,
            reflect_on_tool_use=True,
            output_content_type=SummaryResponse
        )


if __name__ == '__main__':
    import asyncio
    from typing import List, Annotated

    from autogen_agentchat.messages import TextMessage, BaseChatMessage
    from autogen_core import CancellationToken

    current_plan = "检索宁德时代官方年报、招股书及公司官网，提取基础信息：成立时间、创始人背景、注册地、股权结构等基础档案。"

    summary_agent = SummaryAgent()
    messages: List[BaseChatMessage] = [
        TextMessage(source="user", content=f"当前计划：{current_plan}"),
    ]
    cancellation_token = CancellationToken()

    response = asyncio.run(summary_agent.on_messages(messages, cancellation_token))
    print(response.chat_message.content)

    reflect_agent = ReflectAgent()
    messages.append(TextMessage(source="assistant", content=response.chat_message.content.content))

    response = asyncio.run(reflect_agent.on_messages(messages, cancellation_token))
    print(response.chat_message.content)

    iterate_agent = IterateAgent()
    messages.append(TextMessage(source="assistant", content=response.chat_message.content.content))

    response = asyncio.run(iterate_agent.on_messages(messages, cancellation_token))
    print(response.chat_message.content)
