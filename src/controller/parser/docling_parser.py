#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
import base64

import httpx

from config import DOCLING_PARSER_URL
from common.logger import logger, async_time_cost
from common import g
from controller.parser.base import ParserBase, Chunker
from controller.engine import EmbeddingEngine
from controller.repository import Doc, DocStatus, LegacyOfficeFormat, SpecialSupportFormat
from controller.system_config import Strategy


class DoclingParser(ParserBase):
    def __init__(self,
                 doc_id: int,
                 chunker: Chunker,
                 doc: dict = None):
        super().__init__(doc_id=doc_id,
                         chunker=chunker)

        self.parse_timeout = 3600

        # strategy解析配置,从load_strategy读取
        self.embedding_engine: EmbeddingEngine | None = None
        self.embedding_batch_size: int = 0
        self.force_ocr: bool = True
        self.do_table_structure: bool = True
        self.do_formula_enrichment: bool = True
        self.include_images: bool = True
        self.images_scale: float = 2.0

        self.doc = doc

    async def parsing(self):
        await self.load_tenant_strategy()

        await Doc.update(doc_id=self.doc_id, status=DocStatus.parsing)
        await g.session.commit()

        suffix = self.local_path.suffix

        if suffix in LegacyOfficeFormat.__members__.values():
            # 使用LibreOffice进行预转换
            await self.libreoffice_convert(target_format={
                ".doc": 'docx:"MS Word 2007 XML"',
                ".xls": "xlsx",
                ".ppt": "pptx"
            })

        if suffix in SpecialSupportFormat.__members__.values():
            if suffix == SpecialSupportFormat.txt:
                self._txt_to_html()

        if not self.html:
            self.html = await self.docling_source_request()

        # await self.upload_html()

        logger.info(f"{self.log_prefix}parsing阶段完成")

    async def load_tenant_strategy(self):
        parser_strategy = await Strategy.get_parser_strategy(tenant_id=self.tenant_id)
        self.embedding_engine = parser_strategy["embedding_engine"]
        self.embedding_batch_size = parser_strategy["embedding_batch_size"]
        self.force_ocr = parser_strategy["force_ocr"]
        self.do_table_structure = parser_strategy["do_table_structure"]
        self.do_formula_enrichment = parser_strategy["do_formula_enrichment"]
        self.include_images = parser_strategy["include_images"]
        self.images_scale = parser_strategy["images_scale"]

    @async_time_cost()
    async def docling_source_request(self) -> str | None:
        logger.info(f"{self.log_prefix}请求解析")

        async with httpx.AsyncClient(timeout=self.parse_timeout) as client:
            try:
                response = await self.docling_v1_request()
                response.raise_for_status()
            except Exception as err:
                logger.error(f"{self.log_prefix}解析失败: {err}")
                raise err

        document = response.json()["document"]

        html_content = self._process_html_content(document["html_content"])

        return html_content

    @async_time_cost()
    async def docling_v1_request(self):
        async with httpx.AsyncClient(timeout=self.parse_timeout) as client:
            parameters = {
                "from_formats": ["docx", "pptx", "html", "image", "pdf", "asciidoc", "md", "xlsx"],
                "to_formats": ["html"],
                "image_export_mode": "embedded",
                "do_ocr": True,
                "ocr_engine": "rapidocr",
                "ocr_lang": ["english", "chinese"],
                "pdf_backend": "dlparse_v4",
                "table_mode": "accurate",
                "force_ocr": self.force_ocr,
                "include_images": True,
                "images_scale": self.images_scale,
                "do_table_structure": True,
                "do_formula_enrichment": False
            }

            return await client.post(
                url=DOCLING_PARSER_URL,
                data=parameters,
                files={"files": (self.local_path.name, open(self.local_path.as_posix(), mode="rb"))})

    @staticmethod
    def _process_html_content(html_content: str):
        if "\\u003C" in html_content:  # 检测是否包含Unicode转义序列
            html_content = html_content.encode().decode("unicode_escape")

        # 将纯None字符转为空格,多出现在表格中,docling会使用该字符串进行占位的显式提示
        html_content = re.sub(r">(\s*None\s*)<", r"> <", html_content)
        return html_content

    def _txt_to_html(self):
        with open(self.local_path, mode="r", encoding="utf-8") as f:
            lines = f.readlines()

        html_content = "\n".join(f"<p>{line}</p>" for line in lines)

        self.html = html_content
        # 注意这里没上传,并且html文件等待被后续流程覆写




# if __name__ == '__main__':
#     from controller.parser.chunker import HtmlChunker, BGE_M3_TOKENIZER
#     from engine.rdb import session_maker, g
#     import asyncio
#     g.session = session_maker()
#     asyncio.run(DoclingParser(doc_id=4796, chunker=HtmlChunker(tokenizer=BGE_M3_TOKENIZER)).exec())