#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import List
from sqlalchemy import select

from engine.rdb import g, fetch_all, session_maker_sync
from model.auth import RoleModel, RoleName, RoleType


class RoleController:
    @staticmethod
    def get_query(role_id: int = None, role_ids: List[int] = None, types: List[RoleType] = None):
        where = [RoleModel.is_delete == 0]
        if g.tenant_id is not None:
            where.append(RoleModel.tenant_id == g.tenant_id)
        if role_id is not None:
            where.append(RoleModel.id == role_id)
        if role_ids is not None:
            where.append(RoleModel.id.in_(role_ids))
        if types is not None:
            where.append(RoleModel.type_.in_(types))

        query = (
            select(
                RoleModel.id.label("role_id"),
                RoleModel.name.label("role_name"),
                RoleModel.type_.label("role_type")
            )
            .where(*where)
        )
        return query

    async def get_all(self, role_ids: List[int] = None, types: List[RoleType] = None):
        query = self.get_query(role_ids=role_ids, types=types)
        return await fetch_all(query=query)

    @staticmethod
    async def create(tenant_id: int, name: str, type_: RoleType):
        role = RoleModel(type_=type_, tenant_id=tenant_id, name=name)
        g.session.add(role)
        await g.session.flush()

        return role.id

    @staticmethod
    def init_sync():
        with session_maker_sync() as session:
            role = RoleModel(id=1, type_=RoleType.super_admin, tenant_id=1, name=RoleName.super_admin)
            session.add(role)
            session.commit()


Role = RoleController()
