#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import uuid
import math
import asyncio
from typing import List
from pathlib import Path

from config import RerankerModel, EmbeddingModel, LLMModel
from common.logger import logger
from common.time import now_tz_datestring_with_millis
from common import g
from model.doc import get_index
from controller.engine import Embedding<PERSON>ngine, RerankEngine, ChatMessage
from controller.retriever.base import BaseRetriever, async_time_cost, RewriteEngine
from controller.retriever.web import WebSearchConfig, SearchEngine, SearchEngineTokens, WebRetriever
from controller.retriever.repo import RepoRetriever
from controller.operator.chunking.base import RetrieveChunkModel, DocModel as LLMDoc_model
from controller.stats import TokenCounts, LLMBusiness


class HybridRetriever(BaseRetriever):
    """混合检索系统，结合BM25和向量检索方法进行文档搜索
    
    该类实现了一个混合检索系统，通过组合BM25关键词匹配和向量相似度计算两种方式
    对文档进行检索和排序，以提高检索的准确性和多样性。
    """
    def __init__(self,
                 request_id: str = None,
                 repo_search: bool = True,
                 web_search: WebSearchConfig | None = None,
                 repo_ids: list[int] = None,
                 doc_ids: list[int] = None,
                 custom_boost: list[dict] = None,
                 max_doc_size: int = 10,
                 doc_threshold: int = 20,
                 max_doc_chunk_size: int = 10,
                 bm25_weight: float = 1,
                 embedding_weight: float = 1,
                 rewrite_engine: RewriteEngine | None = RewriteEngine(LLMModel.QWEN3_30B_INSTRUCT),
                 embedding_engine: EmbeddingEngine | None = EmbeddingEngine(EmbeddingModel.BGE_M3),
                 rerank_engine: RerankEngine | None = RerankEngine(RerankerModel.BGE_RERANKER_V2_M3),
                 rerank_max_size: int = 30,
                 rerank_threshold: float = None,
                 topn: int = 20,
                 keep_repo_size: int = 0) -> None:
        """
        构建混合检索
        该混合搜索目前包含2个子召回器
            RepoRetriever: 同时使用BM25和向量检索方法对知识库文档进行检索
            WebRetriever: 根据配置,通过网络搜索引擎对互联网文档进行检索,默认使用智谱Pro
        todo: 时间序列的权重控制

        Args:
            request_id: 检索请求的唯一标识符，如果为None则自动生成
            repo_search: 是否开启知识库搜索
            repo_ids: 待检索的知识库ID列表 开启repo_search时有效
            doc_ids: 待检索的文档ID列表 开启repo_search时有效
            custom_boost: 文档召回的boost要求 由用户自定义
            max_doc_size: 保留最多多少个文档筛选范围 必须非None
            max_doc_chunk_size: 每个文档保留最多多少片段数, None时自动计算
            bm25_weight: BM25检索结果的权重系数，默认为1
            embedding_weight: 向量检索结果的权重系数，默认为1
            rewrite_engine: 问题改写引擎实例
            embedding_engine: 文本向量化引擎实例
            rerank_engine: 检索结果重排序引擎实例
            rerank_max_size: rerank engine放入的片段数,无rerank_engine则无效
            rerank_threshold: rerank engine的保留阈值,无rerank_engine则无效
            web_search: 是否启用网络搜索
            topn: 最终最大片段数量
            keep_repo_size: 如果知识库和网络搜索同时开启,最少保留几个知识库片段

        Returns:
            None: 
        """
        if bm25_weight < 0 or embedding_weight < 0 or (bm25_weight + embedding_weight) < 0:
            raise ValueError("bm25_weight/embedding_weight必须>=0, 且加和>0")
        if (not embedding_engine) and embedding_weight > 0:
            logger.warning("由于没有embedding_engine, embedding_weight将被强制等于0")

        request_id = request_id or str(uuid.uuid4())
        super().__init__(
            request_id=request_id,
            rewrite_engine=rewrite_engine,
            rerank_engine=rerank_engine,
            rerank_max_size=rerank_max_size,
            rerank_threshold=rerank_threshold,
            log_prefix=f"混合召回[{request_id}] ",
            bm25_weight=bm25_weight,
            custom_boost=custom_boost
        )
        # 知识库搜索参数
        self.repo_search = repo_search or bool(doc_ids)
        self.repo_retriever: BaseRetriever | None = None
        self.repo_ids = repo_ids
        self.doc_ids = doc_ids
        self.max_doc_size = max_doc_size
        self.doc_threshold = doc_threshold
        self.max_doc_chunk_size = max_doc_chunk_size
        self.embedding_engine = embedding_engine
        self.embedding_weight = embedding_weight
        self.index = get_index(repo_ids=repo_ids)  # 索引index范围

        # 网络搜索参数
        self.web_search = web_search

        # 最终结果参数
        self.topn = topn
        self.keep_repo_size = keep_repo_size

        # [记录字段] 方便外部取用
        self.repo_retriever: RepoRetriever | None = None
        self.web_retriever: WebRetriever | None = None
        self.search_start_time: str | None = None
        self.search_end_time: str | None = None
        self.rerank_end_time: str | None = None

    @async_time_cost("召回总耗时")
    async def retrieve(self, query: str, history: list[ChatMessage] = None) -> List[RetrieveChunkModel]:
        await self.check_plan()
        self.query = query.strip()
        self.history = history
        logger.info(self.log_prefix + f"开始文档召回: {self.query}")

        await self.rewriting()

        tasks = []
        if self.repo_search:
            self.repo_retriever = repo_retriever = RepoRetriever(
                request_id=self.request_id,
                repo_ids=self.repo_ids,
                doc_ids=self.doc_ids,
                custom_boost=self.custom_boost,
                max_doc_size=self.max_doc_size,
                doc_threshold=self.doc_threshold,
                max_doc_chunk_size=self.max_doc_chunk_size,
                bm25_weight=self.bm25_weight,
                embedding_weight=self.embedding_weight,
                embedding_engine=self.embedding_engine,
                topn=self.topn,
                query=self.query_rewrite or self.query,
                query_rewrite=self.query_rewrite,
                query_keywords=self.query_keywords,
                query_associative_keywords=self.query_associative_keywords,
                query_search_terms=self.query_search_terms,
                query_keyword_weights=self.query_keyword_weights,
                query_token_weights=self.query_token_weights
            )
            tasks.append(repo_retriever.searching())
        if self.web_search:
            self.web_retriever = web_retriever = self.web_search.retriever(
                search_config=self.web_search.search_config,
                request_id=self.request_id,
                max_doc_chunk_size=self.max_doc_chunk_size,
                topn=self.topn,
                bm25_weight=self.bm25_weight,
                custom_boost=self.custom_boost,
                query=self.query_rewrite or self.query,
                query_rewrite=self.query_rewrite,
                query_keywords=self.query_keywords,
                query_associative_keywords=self.query_associative_keywords,
                query_search_terms=self.query_search_terms,
                query_keyword_weights=self.query_keyword_weights,
                query_token_weights=self.query_token_weights
            )
            tasks.append(web_retriever.searching())

        self.search_start_time = now_tz_datestring_with_millis()
        tasks_result = await asyncio.gather(*tasks)
        source_chunks = []
        for task_result in tasks_result:
            source_chunks.extend(task_result)
        self.search_end_time = now_tz_datestring_with_millis()

        source_chunks = self.coarse_ranking(source_chunks=source_chunks)
        source_chunks = await self.model_reranking(source_chunks=source_chunks)
        self.resort_chunks(source_chunks=source_chunks)
        self.rerank_end_time = now_tz_datestring_with_millis()

        await self.record_token()

        return self.chunks

    @async_time_cost("召回总耗时")
    async def retrieve_docs(self, query: str, history: list[ChatMessage] = None) -> List[LLMDoc_model]:
        await self.check_plan()
        self.query = query.strip()
        self.history = history
        logger.info(self.log_prefix + f"开始文档召回: {self.query}")

        await self.rewriting()

        tasks = []
        if self.repo_search:
            self.repo_retriever = repo_retriever = RepoRetriever(
                request_id=self.request_id,
                repo_ids=self.repo_ids,
                doc_ids=self.doc_ids,
                custom_boost=self.custom_boost,
                max_doc_size=self.max_doc_size,
                bm25_weight=self.bm25_weight,
                embedding_weight=self.embedding_weight,
                embedding_engine=self.embedding_engine,
                topn=self.topn,
                query=self.query_rewrite or self.query,
                query_rewrite=self.query_rewrite,
                query_keywords=self.query_keywords,
                query_associative_keywords=self.query_associative_keywords,
                query_search_terms=self.query_search_terms,
                query_keyword_weights=self.query_keyword_weights,
                query_token_weights=self.query_token_weights
            )
            tasks.append(repo_retriever.searching_docs())
        if self.web_search:
            self.web_retriever = web_retriever = self.web_search.retriever(
                search_config=self.web_search.search_config,
                request_id=self.request_id,
                topn=self.topn,
                bm25_weight=self.bm25_weight,
                custom_boost=self.custom_boost,
                query=self.query_rewrite or self.query,
                query_rewrite=self.query_rewrite,
                query_keywords=self.query_keywords,
                query_associative_keywords=self.query_associative_keywords,
                query_search_terms=self.query_search_terms,
                query_keyword_weights=self.query_keyword_weights,
                query_token_weights=self.query_token_weights
            )
            tasks.append(web_retriever.searching_docs())

        self.search_start_time = now_tz_datestring_with_millis()
        tasks_result = await asyncio.gather(*tasks)
        source_chunks = []
        for task_result in tasks_result:
            source_chunks.extend(task_result)
        self.search_end_time = now_tz_datestring_with_millis()

        await self.record_token()

        return source_chunks

    def coarse_ranking(self, source_chunks: List[RetrieveChunkModel]):
        """由于切片会导致片段中可能丢失上下文的核心词,导致可能无关的某个片段因为获得某个词的累积效应而排在较高位置
        为了解决这个问题,在Python层面做后处理,在粗筛后重新进行排序
        注意: 此方法只依靠idf思想,与原始算法并无关系;重算池子中TOPN相关文档的词频出现率对分数进行提权,仍依赖ES分数;

        rerank是不稳定的方法,只对一些极端情况有正向作用,故生效情况需要较为严格.目前对文档进行分数增强需要经过2个条件: 该词出现的较少/包含该词的文档较少
        """
        if not source_chunks:
            return source_chunks

        rerank_tokens = {word: weight for word, weight in self.query_token_weights.items() if
                         len(word) > 1 and weight > 0.5}
        query_keyword_count = {word: 0 for word, weight in rerank_tokens.items()}
        doc_contains_count = {word: 0 for word, weight in rerank_tokens.items()}
        # 计算平均分数,兼容没有分数的网络搜索
        if scores := [chunk.score for chunk in source_chunks if chunk.score]:
            avg_score = sum(scores) / len(source_chunks)
        else:
            avg_score = 1

        for chunk in source_chunks:
            if chunk.score is None:
                chunk.score = avg_score
            chunk.judge_text = ("-".join(chunk.title) + chunk.plain_content
                                if chunk.title
                                else chunk.plain_content).replace("\n", "").replace(" ", "")
            # 加和每个结果的关键词数量
            for kw in query_keyword_count:
                query_keyword_count[kw] += chunk.judge_text.count(kw)
                doc_contains_count[kw] += 1 if kw in chunk.judge_text else 0

        # 总平均词频 = 总词频数 / 词数 + 1
        if not query_keyword_count:
            avg_kw_count = 1
        else:
            avg_kw_count = int(sum(list(query_keyword_count.values())) / len(query_keyword_count) + 1)
        # 为低词频文档加权
        for word, count in query_keyword_count.items():
            if count == 0:
                continue

            # 单个词权重倍率 = 总平均词频 / 无变动倍率 / 单词词频
            weight = avg_kw_count / 1.5 / count
            # 无变动倍率保证单词词频略微超过总平均词频时,不会产生分数变动
            # 包含该词的文档占比 / 召回文档数 >= 0.25时,不会产生分数变动
            if weight <= 1 or doc_contains_count[word] / len(source_chunks) >= 0.25:
                continue

            # 分数放大倍率
            low_freq_max_weight = 3  # 单词最大分数放大率
            ratio = min(1 + math.log(weight), low_freq_max_weight)  # [1, low_freq_max_weight]
            for chunk in source_chunks:
                if word in chunk.judge_text:
                    chunk.score *= ratio

        # 返回粗排后topn1.5倍数量的片段
        source_chunks = list(sorted(source_chunks, key=lambda x: x.score, reverse=True))
        return source_chunks[:self.rerank_max_size if self.rerank_engine else self.topn]

    @async_time_cost()
    async def model_reranking(self, source_chunks: list[RetrieveChunkModel]):
        if not self.rerank_engine:
            return source_chunks
        if len(source_chunks) == 1:
            return source_chunks

        rerank_text_mapping = {}
        cid_scores = {}
        for chunk in source_chunks:
            filename_title_prefix = f"{Path(chunk.filename).stem} \n{'_'.join(chunk.title)} \n"
            prefix_token_counts = len(filename_title_prefix)
            cid_scores[chunk.cid] = []
            if prefix_token_counts + chunk.token_counts <= self.rerank_max_tokens:
                rerank_text = f"{filename_title_prefix}{chunk.plain_content}"
                rerank_text_mapping[rerank_text] = chunk.cid
            else:
                for step in range(0, len(chunk.plain_content), self.rerank_max_tokens - prefix_token_counts):
                    step_text = chunk.plain_content[step:step+self.rerank_max_tokens-prefix_token_counts]
                    rerank_text = f"{filename_title_prefix}{step_text}"
                    rerank_text_mapping[rerank_text] = chunk.cid

        rerank_query = self.query_rewrite or self.query
        rerank_query = rerank_query.strip(" ?？！!。.~")
        if self.query_associative_keywords:
            rerank_query += f" {' '.join(self.query_associative_keywords)}"
        if self.query_search_terms:
            rerank_query += f" {' '.join(self.query_search_terms)}"

        rerank_texts = list(rerank_text_mapping.keys())
        for step in range(0, len(rerank_texts), self.rerank_batch_size):
            batch_texts = rerank_texts[step:step+self.rerank_batch_size]
            res = await self.rerank_engine.rerank(query=rerank_query, docs=batch_texts)
            for r in res:
                cid = rerank_text_mapping[batch_texts[r["index"]]]
                cid_scores[cid].append(r["relevance_score"])
        for chunk in source_chunks:
            chunk.relevance_score = max(cid_scores[chunk.cid]) if cid_scores[chunk.cid] else 0

        source_chunks = list(sorted(source_chunks, key=lambda x: x.relevance_score, reverse=True))
        if self.rerank_threshold:
            source_chunks = [chunk for chunk in source_chunks if chunk.relevance_score >= self.rerank_threshold]
        return source_chunks

    def resort_chunks(self, source_chunks: List[RetrieveChunkModel]):
        if self.repo_search and self.web_search and len(source_chunks) > self.topn and self.keep_repo_size:
            repo_count = 0
            for i, chunk in enumerate(source_chunks):
                if chunk.web_search is False:
                    repo_count += 1
                self.chunks.append(chunk)

                if self.topn - repo_count == i + 1:
                    for j in range(i + 1, len(source_chunks)):
                        if source_chunks[j].web_search is False:
                            self.chunks.append(source_chunks[j])
                    break
        else:
            self.chunks = source_chunks
        self.chunks = self.chunks[:self.topn]

    @staticmethod
    async def check_plan():
        if not g.tenant_id:
            return
        await TokenCounts.check_plan(tenant_id=g.tenant_id)

    async def record_token(self):
        if not g.tenant_id:
            return

        if self.rewrite_engine:
            await TokenCounts.record(
                tenant_id=g.tenant_id, model_name=self.rewrite_engine.chat_engine.model_name,
                input_tokens=self.rewrite_engine.chat_engine.input_token,
                output_tokens=self.rewrite_engine.chat_engine.output_token,
                business=LLMBusiness.chat, create_user_id=g.user_id)

        if self.web_search:
            if self.web_search.name == SearchEngine.zhipu:
                await TokenCounts.record(
                    tenant_id=g.tenant_id, model_name=self.web_search.name, input_tokens=0,
                    output_tokens=SearchEngineTokens.zhipu, business=LLMBusiness.web_search, create_user_id=g.user_id)
            if self.web_search.name == SearchEngine.tavily:
                await TokenCounts.record(
                    tenant_id=g.tenant_id, model_name=self.web_search.name, input_tokens=0,
                    output_tokens=SearchEngineTokens.tavily, business=LLMBusiness.web_search, create_user_id=g.user_id)

        await g.session.commit()



if __name__ == '__main__':
    from engine.rdb import session_maker
    g.session = session_maker()
    hybrid = HybridRetriever(repo_ids=[39], max_doc_size=10, max_doc_chunk_size=10, doc_threshold=20)
    docs = asyncio.run(hybrid.retrieve(query="达观数据的婚假是多少天？"))
    print(docs)
