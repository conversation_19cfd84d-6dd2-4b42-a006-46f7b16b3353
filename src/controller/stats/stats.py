#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import uuid
import json
from typing import List

import numpy as np
import pandas as pd
from enum import StrEnum
from datetime import datetime, timedelta

from engine.es import es
from engine.cache import r_cache
from model.doc import get_index
from common import g
from common.logger import logger
from controller.engine.chat import ChatEngine, LLMModel
from controller.stats.token_counts import TokenCounts, LLMBusiness


class StatsContent(StrEnum):
    last_docs = "last_docs"  # 最新数据
    doc_count_trend = "doc_count_trend"  # 每日数据量走势
    sentiment_score_range = "sentiment_score_range"  # 情感区间文档数统计
    sentiment_score_trend = "sentiment_score_trend"  # 情感分数每日均值走势
    top_sentiment_docs = "top_sentiment_docs"  # 情绪最高和最低的文档
    top_sentiment_tags = "top_sentiment_tags"  # 情绪最高和最低的标签
    top_subject = "top_subject"  # 出现最多的实体
    top_doc_keywords = "top_doc_keywords"  # 出现最多的关键词
    top_view_keywords = "top_view_keywords"  # 出现最多的正面和负面观点关键词
    view_count_ratio_trend = "view_count_ratio_trend"  # 每日正面和负面观点比例走势


class StatsController:
    chat_engine = ChatEngine(model_name=LLMModel.QWEN3_30B_INSTRUCT)

    async def get_doc_stats(self, stats_contents: list[StatsContent], keywords: list[str] = None,
                            exclude_keywords: list[str] = None, repo_ids: list[int] = None, start: str = None,
                            end: str = None, size: int = 0):
        # 时间补全
        index = get_index(repo_ids=repo_ids)
        if not start or not end:
            data_start, data_end = await self.get_date_range_from_index(index=index)
            if not start:
                start = data_start
            if not end:
                end = data_end

        aggs = {}
        process_funcs = []
        if StatsContent.last_docs in stats_contents:
            # 仅有func
            process_funcs.append(self.last_docs())
            size = 5

        if StatsContent.doc_count_trend in stats_contents:
            condition, process_func = await self.doc_count_trend(start=start, end=end)
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.sentiment_score_range in stats_contents:
            condition, process_func = self.sentiment_score_range()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.sentiment_score_trend in stats_contents:
            condition, process_func = await self.sentiment_score_trend(start=start, end=end)
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.top_sentiment_docs in stats_contents:
            condition, process_func = self.top_sentiment_docs()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.top_sentiment_tags in stats_contents:
            condition, process_func = self.top_sentiment_tags()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.top_subject in stats_contents:
            condition, process_func = self.top_subject()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.top_doc_keywords in stats_contents:
            condition, process_func = self.top_doc_keywords()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.top_view_keywords in stats_contents:
            condition, process_func = self.top_view_keywords()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.view_count_ratio_trend in stats_contents:
            condition, process_func = await self.top_view_ratio_trend(start=start, end=end)
            aggs.update(condition)
            process_funcs.append(process_func)

        # 构建查询
        filters = [
            {
                "range": {
                    "data_time": {
                        "gte": start,
                        "lte": end
                    }
                },
            },
            {
                "term": {
                    "tenant_id": g.tenant_id
                }
            }
        ]

        should = []
        must_nots = []
        if keywords:
            for kw in keywords:
                # 对text字段使用phrase查询，对keyword字段使用普通multi_match
                should.extend([
                    {
                        "multi_match": {
                            "query": kw,
                            "type": "phrase",
                            "fields": ["plain_text", "filename"],
                            "analyzer": "ik_max_word"
                        }
                    },
                    {
                        "multi_match": {
                            "query": kw,
                            "fields": ["author", "source", "tags"]
                        }
                    }
                ])

        if exclude_keywords:
            for kw in exclude_keywords:
                # 对text字段使用phrase查询，对keyword字段使用普通multi_match
                must_nots.extend([
                    {
                        "multi_match": {
                            "query": kw,
                            "type": "phrase",
                            "fields": ["plain_text", "filename"],
                            "analyzer": "ik_max_word"
                        }
                    },
                    {
                        "multi_match": {
                            "query": kw,
                            "fields": ["author", "source", "tags"]
                        }
                    }
                ]
            )

        bool_query = {
            "bool": {
                "filter": filters,
                "must_not": must_nots,
            }
        }
        if should:
            bool_query["bool"]["should"] = should
            bool_query["bool"]["minimum_should_match"] = 1

        res = await es.search(
            index=index,
            query=bool_query,
            aggs=aggs,
            size=size,
            track_total_hits=True,
            source_includes=["data_time", "doc_id", "filename", "repo_id"],
            sort=[{"data_time": "desc"}])

        return_stats = {
            "total": res["hits"]["total"]["value"],
            "start": start,
            "end": end
        }
        for func in process_funcs:
            return_stats.update(func(res))

        return return_stats

    @staticmethod
    async def get_date_range_from_index(index: str):
        """获取索引中最老和最新的数据时间"""
        res = await es.search(
            index=index,
            query={"match_all": {}},
            aggs={
                "min_date": {
                    "min": {
                        "field": "data_time"
                    }
                },
                "max_date": {
                    "max": {
                        "field": "data_time"
                    }
                }
            },
            size=0
        )
        min_date = None
        max_date = None
        if res["aggregations"]["min_date"]["value"]:
            # 有min就有max
            min_date = res["aggregations"]["min_date"]["value_as_string"]
            max_date = res["aggregations"]["max_date"]["value_as_string"]

        return min_date, max_date

    @staticmethod
    def last_docs():
        return lambda res: {"last_docs": [hit["_source"] for hit in res["hits"]["hits"]]}

    async def doc_count_trend(self, start: str, end: str):
        """每日数据量走势"""
        # 确保时间格式为日期格式
        start_date = datetime.strptime(start[:10], "%Y-%m-%d")
        end_date = datetime.strptime(end[:10], "%Y-%m-%d")
        start = start_date.strftime("%Y-%m-%d")
        end = end_date.strftime("%Y-%m-%d")

        condition = {
            "doc_count_trend": {
                "date_histogram": {
                    "field": "data_time",
                    "calendar_interval": "1d",
                    "format": "yyyy-MM-dd",
                    "min_doc_count": 0,
                    "extended_bounds": {
                        "min": start,
                        "max": end
                    }
                }
            }
        }

        def process_func(res):
            # 生成完整的日期范围
            current_date = start_date
            date_range = []
            while current_date <= end_date:
                date_range.append(current_date.strftime("%Y-%m-%d"))
                current_date += timedelta(days=1)

            # 从ES结果中提取数据
            buckets = res["aggregations"]["doc_count_trend"]["buckets"]
            data_dict = {}
            for bucket in buckets:
                date_key = bucket["key_as_string"]
                doc_count = bucket["doc_count"]
                data_dict[date_key] = doc_count

            # 填充缺失日期为0
            result = []
            for date_str in date_range:
                result.append({
                    "date": date_str,
                    "value": data_dict.get(date_str, 0)
                })

            return {
                "doc_count_trend": result,
                "doc_count_desc": self.describe(data=result, index="date")
            }

        return condition, process_func

    @staticmethod
    def sentiment_score_range(min_positive: int = 35, max_negative: int = -35):
        """情感区间文档数"""
        condition =  {
            # 情感区间文档数
            "sentiment_score_range": {
                "range": {
                    "field": "extract_result.sentiment_score",
                    "ranges": [
                        {"key": "正面", "from": -100, "to": max_negative + 1},  # [0, <-34)
                        {"key": "中性", "from": max_negative + 1, "to": min_positive},  # [-34, 35)
                        {"key": "负面", "from": min_positive, "to": 101}  # [35, 100]
                    ],
                    "keyed": True
                }
            },
        }
        process_func = lambda res: {
            "sentiment_score_range": [
                {"name": key, "value": bucket["doc_count"]}
                for key, bucket in res["aggregations"]["sentiment_score_range"]["buckets"].items()]}

        return condition, process_func


    async def sentiment_score_trend(self, start: str | None, end: str | None):
        """情感分数每日均值走势"""
        # 确保时间格式为日期格式
        if start:
            start_date = datetime.strptime(start[:10], "%Y-%m-%d")
            start = start_date.strftime("%Y-%m-%d")
        if end:
            end_date = datetime.strptime(end[:10], "%Y-%m-%d")
            end = end_date.strftime("%Y-%m-%d")

        condition = {
            "sentiment_score_trend": {
                "date_histogram": {
                    "field": "data_time",
                    "calendar_interval": "1d",
                    "format": "yyyy-MM-dd",
                    "min_doc_count": 0,
                    "extended_bounds": {
                        "min": start,
                        "max": end
                    }
                },
                "aggs": {
                    "avg_sentiment": {
                        "avg": {
                            "field": "extract_result.sentiment_score"
                        }
                    }
                }
            }
        }

        def process_func(res):
            # 生成完整的日期范围
            if not start and not end:
                return {"sentiment_score_trend": []}
            current_date = start_date
            date_range = []
            while current_date <= end_date:
                date_range.append(current_date.strftime("%Y-%m-%d"))
                current_date += timedelta(days=1)

            # 从ES结果中提取数据
            buckets = res["aggregations"]["sentiment_score_trend"]["buckets"]
            data_dict = {}
            for bucket in buckets:
                date_key = bucket["key_as_string"]
                avg_value = bucket["avg_sentiment"]["value"]
                data_dict[date_key] = round(avg_value, 2) if avg_value is not None else 0

            # 填充缺失日期为0
            result = []
            for date_str in date_range:
                result.append({
                    "date": date_str,
                    "value": data_dict.get(date_str, 0)
                })

            return {
                "sentiment_score_trend": result,
                "sentiment_score_desc": self.describe(data=result, index="date")}

        return condition, process_func

    @staticmethod
    def top_sentiment_docs(size: int = 5):
        """情绪最高和最低的文档"""
        condition = {
            # 情绪最高的5条文档
            "top_positive_docs": {
                "terms": {
                    "field": "extract_result.sentiment_score",
                    "size": size,
                    "order": {"_key": "desc"}
                },
                "aggs": {
                    "top_docs": {
                        "top_hits": {
                            "size": size,
                            "_source": ["filename", "extract_result.sentiment_score", "data_time", "doc_id", "repo_id"]
                        }
                    }
                }
            },
            # 情绪最低的5条文档
            "top_negative_docs": {
                "terms": {
                    "field": "extract_result.sentiment_score",
                    "size": size,
                    "order": {"_key": "asc"}
                },
                "aggs": {
                    "top_docs": {
                        "top_hits": {
                            "size": size,
                            "_source": ["filename", "extract_result.sentiment_score", "data_time", "doc_id", "repo_id"]
                        }
                    }
                }
            }
        }
        process_func = lambda res: {
            "top_positive_docs": [
                {"doc_id": hit["_id"], **hit["_source"]}
                for bucket in res["aggregations"]["top_positive_docs"]["buckets"]
                for hit in bucket["top_docs"]["hits"]["hits"]
            ][:5],
            "top_negative_docs": [
                {"doc_id": hit["_id"], **hit["_source"]}
                for bucket in res["aggregations"]["top_negative_docs"]["buckets"]
                for hit in bucket["top_docs"]["hits"]["hits"]
            ][:5]
        }
        return condition, process_func

    @staticmethod
    def top_sentiment_tags():
        """情绪最高和最低的标签"""
        condition = {
            # 情绪最高的标签
            "top_positive_tags": {
                "terms": {
                    "field": "tags",
                    "size": 1,
                    "order": {
                        "avg_sentiment": "desc"
                    }
                },
                "aggs": {
                    "avg_sentiment": {
                        "avg": {
                            "field": "extract_result.sentiment_score"
                        }
                    }
                }
            },
            # 情绪最低的标签
            "top_negative_tags": {
                "terms": {
                    "field": "tags",
                    "size": 1,
                    "order": {
                        "avg_sentiment": "asc"
                    }
                },
                "aggs": {
                    "avg_sentiment": {
                        "avg": {
                            "field": "extract_result.sentiment_score"
                        }
                    }
                }
            }
        }
        process_func = lambda res: {
            "top_positive_tags": [
                {"name": bucket["key"], "value": bucket["avg_sentiment"]["value"]}
                for bucket in res["aggregations"]["top_positive_tags"]["buckets"]
            ],
            "top_negative_tags": [
                {"name": bucket["key"], "value": bucket["avg_sentiment"]["value"]}
                for bucket in res["aggregations"]["top_negative_tags"]["buckets"]
            ]
        }
        return condition, process_func

    @staticmethod
    def top_subject():
        """出现最多的实体"""
        condition = {
            "top_subject": {
                "terms": {
                    "field": "extract_result.subject.keyword",
                    "size": 5
                }
            }
        }
        process_func = lambda res: {
            "top_subject": [
                {"name": bucket["key"], "value": bucket["doc_count"]}
                for bucket in res["aggregations"]["top_subject"]["buckets"]
            ]
        }
        return condition, process_func

    @staticmethod
    def top_doc_keywords(size: int = 50):
        """出现最多的关键词"""
        condition = {
            "top_doc_keywords": {
                "terms": {
                    "field": "keywords",
                    "size": size
                }
            }
        }
        process_func = lambda res: {
            "top_doc_keywords": [
                {"name": bucket["key"], "value": bucket["doc_count"]}
                for bucket in res["aggregations"]["top_doc_keywords"]["buckets"]
            ]
        }
        return condition, process_func

    @staticmethod
    def top_view_keywords(positive_size: int = 50, negative_size: int = 50):
        """出现最多的正面和负面观点关键词"""
        condition = {
            # 出现最多的正面观点关键词
            "top_positive_view_keywords": {
                "terms": {
                    "field": "extract_result.positive_view_keywords",
                    "size": positive_size
                }
            },
            # 出现最多的负面观点关键词
            "top_negative_view_keywords": {
                "terms": {
                    "field": "extract_result.negative_view_keywords",
                    "size": negative_size
                }
            }
        }
        process_func = lambda res: {
            "top_positive_view_keywords": [
                {"name": bucket["key"], "value": bucket["doc_count"]}
                for bucket in res["aggregations"]["top_positive_view_keywords"]["buckets"]
            ],
            "top_negative_view_keywords": [
                {"name": bucket["key"], "value": bucket["doc_count"]}
                for bucket in res["aggregations"]["top_negative_view_keywords"]["buckets"]
            ]
        }
        return condition, process_func

    async def top_view_ratio_trend(self, start: str, end: str):
        """每日正面和负面观点比例走势"""
        # 确保时间格式为日期格式
        start_date = datetime.strptime(start[:10], "%Y-%m-%d")
        end_date = datetime.strptime(end[:10], "%Y-%m-%d")
        start = start_date.strftime("%Y-%m-%d")
        end = end_date.strftime("%Y-%m-%d")

        condition = {
            "top_view_ratio_trend": {
                "date_histogram": {
                    "field": "data_time",
                    "calendar_interval": "1d",
                    "format": "yyyy-MM-dd",
                    "min_doc_count": 0,
                    "extended_bounds": {
                        "min": start,
                        "max": end
                    }
                },
                "aggs": {
                    "positive_view_count": {
                        "sum": {
                            "script": {
                                "source": "if (params._source.extract_result != null && params._source.extract_result.positive_view != null) { return params._source.extract_result.positive_view.size(); } return 0;"
                            }
                        }
                    },
                    "negative_view_count": {
                        "sum": {
                            "script": {
                                "source": "if (params._source.extract_result != null && params._source.extract_result.negative_view != null) { return params._source.extract_result.negative_view.size(); } return 0;"
                            }
                        }
                    }
                }
            }
        }

        def process_func(res):
            # 生成完整的日期范围
            current_date = start_date
            date_range = []
            while current_date <= end_date:
                date_range.append(current_date.strftime("%Y-%m-%d"))
                current_date += timedelta(days=1)

            # 从ES结果中提取数据
            buckets = res["aggregations"]["top_view_ratio_trend"]["buckets"]
            data_dict = {}
            for bucket in buckets:
                date_key = bucket["key_as_string"]
                doc_count = bucket["doc_count"]
                positive_count = bucket["positive_view_count"]["value"]
                negative_count = bucket["negative_view_count"]["value"]

                # 计算比例
                positive_ratio = round(positive_count / doc_count, 4) if doc_count > 0 else 0
                negative_ratio = round(negative_count / doc_count, 4) if doc_count > 0 else 0

                data_dict[date_key] = {
                    "positive_ratio": positive_ratio,
                    "negative_ratio": negative_ratio,
                    "positive_count": int(positive_count),
                    "negative_count": int(negative_count),
                    "total_count": doc_count
                }

            # 填充缺失日期为0
            result = []
            for date_str in date_range:
                if date_str in data_dict:
                    result.append({
                        "date": date_str,
                        **data_dict[date_str]
                    })
                else:
                    result.append({
                        "date": date_str,
                        "positive_ratio": 0,
                        "negative_ratio": 0,
                        "positive_count": 0,
                        "negative_count": 0,
                        "total_count": 0
                    })
            top_positive_view_ratio_trend = [{"name": r["date"], "value": r["positive_ratio"]} for r in result]
            top_negative_view_ratio_trend = [{"name": r["date"], "value": r["negative_ratio"]} for r in result]
            return {
                "positive_view_count_ratio_trend": top_positive_view_ratio_trend,
                "negative_view_count_ratio_trend": top_negative_view_ratio_trend,
                "positive_view_count_ratio_describe": self.describe(data=top_positive_view_ratio_trend, index="name"),
                "negative_view_count_ratio_describe": self.describe(data=top_negative_view_ratio_trend, index="name"),
            }

        return condition, process_func

    @staticmethod
    async def set_cache(data: dict):
        name = f"stats:{uuid.uuid4().hex}"
        await r_cache.set(name=name, value=json.dumps(data, ensure_ascii=False, ex=600))
        return name

    @staticmethod
    async def get_cache(name: str):
        data = await r_cache.get(name=name)
        if data is not None:
            return json.loads(data)
        raise None

    @staticmethod
    def describe(data: List[dict], index: str):
        df = pd.DataFrame(data)
        df.set_index(index, inplace=True)

        desc = {
            "均值": df.mean(),
            "中位数": df.median(),
            "标准差": df.std(),
            "最小值": df.min(),
            "最大值": df.max(),
            "变异系数(%)": df.std() / df.mean() * 100,  # 百分比形式,
            "25%分位数": df.quantile(0.25),
            "75%分位数": df.quantile(0.75),
            "四分位距": df.quantile(0.75) - df.quantile(0.25),
            "偏度": df.skew(),
            "峰度": df.kurtosis()
        }

        return [{"name": k, "value": round(float(desc[k].values[0]), 2) if not np.isnan(desc[k].values[0]) else None} for k, v in desc.items()]

    @staticmethod
    def data_analyze_prompt(data: dict):
        doc_count_trend_series = {item["date"]: item["value"] for item in data["doc_count_trend"]}
        sentiment_score_trend = {item["date"]: item["value"] for item in data["sentiment_score_trend"]}
        positive_view_count_ratio_trend = {item["name"]: item["value"] for item in
                                           data["positive_view_count_ratio_trend"]}
        negative_view_count_ratio_trend = {item["name"]: item["value"] for item in
                                           data["negative_view_count_ratio_trend"]}

        trend_df = pd.DataFrame({
            "数据量": doc_count_trend_series,
            "情感": sentiment_score_trend,
            "正面观点数比例": positive_view_count_ratio_trend,
            "负面观点数比例": negative_view_count_ratio_trend
        })

        # 如果需要将索引转换为日期时间类型
        trend_df.index = pd.to_datetime(trend_df.index)
        trend_df = trend_df.round(2)

        desc = "**{item}**: {desc_str}"
        describes = [
            desc.format(item="数据量",
                        desc_str=" ".join([f"{v['name']}:{v['value']}" for v in data["doc_count_desc"]])),
            desc.format(item="情感",
                        desc_str=" ".join([f"{v['name']}:{v['value']}" for v in data["sentiment_score_desc"]])),
            desc.format(item="正面观点数比例", desc_str=" ".join(
                [f"{v['name']}:{v['value']}" for v in data["positive_view_count_ratio_describe"]])),
            desc.format(item="负面观点数比例", desc_str=" ".join(
                [f"{v['name']}:{v['value']}" for v in data["negative_view_count_ratio_describe"]]))
        ]
        trend_describe = "\n".join(describes)

        score_range_desc = "\n".join([f"{v['name']}:{v['value']}" for v in data['sentiment_score_range']])
        top_object_desc = "\n".join([f"{v['name']}:{v['value']}" for v in data['top_subject']])
        top_doc_keywords_desc = "\n".join([f"{v['name']}:{v['value']}" for v in data['top_doc_keywords']])
        top_positive_view_keywords_desc = "\n".join(
            [f"{v['name']}:{v['value']}" for v in data['top_positive_view_keywords']])
        top_negative_view_keywords_desc = "\n".join(
            [f"{v['name']}:{v['value']}" for v in data['top_negative_view_keywords']])

        template = f"""你是一个专业的数据分析师，请从数理角度，帮助用户分析其提供的数据,递进的阐述数据分析结果。
请确保你的分析和观点基于提供的数据，并且分析过程清晰、观点有一定的深度和实用性。

## 一、数据内容和关注点

**记住以下数据内容，绝不允许数值错误**

<数据量、情感、正面观点数、负面观点数>
{trend_df.to_markdown()}

以上表格的描述性统计分析如下：
{trend_describe}
</数据量、情感、正面观点数、负面观点数>

<情感分布>数据中正面、中性、负面的分布次数
{score_range_desc}
</情感分布>

<实体>数据中抽取出的最多重复的实体及次数
{top_object_desc}
</实体>

<关键词>数据中抽取出的最高词频关键词及次数
{top_doc_keywords_desc}
</关键词>

<正面观点关键词>数据中抽取出正面观点中最高频出现的关键词及次数
{top_positive_view_keywords_desc}
</正面观点关键词>

<负面观点关键词>数据中抽取出负面观点中最高频出现的关键词及次数
{top_negative_view_keywords_desc}
</负面观点关键词>

## 二、任务要求

总体要求：**严禁编造**，表述的专业度必须以专业数据分析师为标准。

### 统计分析
对<数据量、情感、正面观点数、负面观点数>进行统计分析，要求如下：
1. 对<数据量、情感、正面观点数、负面观点数>逐一说明均值、中位数、偏态/峰态，及数理论述推断
2. 对<情感分布><实体><关键词><正面观点关键词><负面观点关键词>做数据分析推断
3. **注意**: 当指出某特定日期数据时，需要完整的说明对应日期

### 数据总结
根据所有数据内容，总结出你认为有价值的数据摘要和业务洞察，要求如下：
1. 对0值数据无分析必要时不提及
2. 字数控制在300字左右，输出时不必提示字数多少

### 建议或关注点
根据以上总结内容，给出你认为的业务建议或关注点，要求如下：
1. 字数控制在200字左右，输出时不必提示字数多少
2. 只有在对建议和关注点有较强数据支撑的情况下才输出

## 三、输出格式和示例
请按照如下顺序进行输出回复,其中数据说明已经提供，直接复述即可

---
<数据说明>
根据筛选条件，在数据起止时间 {data['start']} 至 {data['end']} 中：已查询到数据总量 {data['total']}，AI分析成功数量 {sum([v['value'] for v in data['sentiment_score_range']])}，分析结果如下：

<统计分析>
- **数据量**：...
- **情感**：...
- **正面观点数**：...
- **负面观点数**：...
- **情感分布**：...
- **高频实体**：...
- **高频关键词**：...
- **高频正面观点关键词**：...
- **高频负面观点关键词**：...

<数据总结>
...

<建议和关注点>
...

---

现在,请完整考虑以上所有要求，给出你的最终回答"""
        return template

    @staticmethod
    def data_compare_prompt(left: dict, right: dict):
        res = {}
        for side, data in [("left", left), ("right", right)]:
            doc_count_trend_series = {item["date"]: item["value"] for item in data["doc_count_trend"]}
            sentiment_score_trend = {item["date"]: item["value"] for item in data["sentiment_score_trend"]}
            positive_view_count_ratio_trend = {item["name"]: item["value"] for item in data["positive_view_count_ratio_trend"]}
            negative_view_count_ratio_trend = {item["name"]: item["value"] for item in data["negative_view_count_ratio_trend"]}

            trend_df = pd.DataFrame({
                "数据量": doc_count_trend_series,
                "情感": sentiment_score_trend,
                "正面观点数比例": positive_view_count_ratio_trend,
                "负面观点数比例": negative_view_count_ratio_trend
            })
            trend_df.index = pd.to_datetime(trend_df.index)
            trend_df = trend_df.round(2)

            desc = "**{item}**: {desc_str}"
            describes = [
                desc.format(item="数据量", desc_str=" ".join([f"{v['name']}:{v['value']}" for v in data["doc_count_desc"]])),
                desc.format(item="情感", desc_str=" ".join([f"{v['name']}:{v['value']}" for v in data["sentiment_score_desc"]])),
                desc.format(item="正面观点数比例", desc_str=" ".join([f"{v['name']}:{v['value']}" for v in data["positive_view_count_ratio_describe"]])),
                desc.format(item="负面观点数比例", desc_str=" ".join([f"{v['name']}:{v['value']}" for v in data["negative_view_count_ratio_describe"]]))
            ]
            trend_describe = "\n".join(describes)

            score_range_desc = "\n".join([f"{v['name']}:{v['value']}" for v in data['sentiment_score_range']])
            top_object_desc = "\n".join([f"{v['name']}:{v['value']}" for v in data['top_subject']])
            top_doc_keywords_desc = "\n".join([f"{v['name']}:{v['value']}" for v in data['top_doc_keywords']])
            top_positive_view_keywords_desc = "\n".join([f"{v['name']}:{v['value']}" for v in data['top_positive_view_keywords']])
            top_negative_view_keywords_desc = "\n".join([f"{v['name']}:{v['value']}" for v in data['top_negative_view_keywords']])

            res[side] = {
                "trend_df": trend_df,
                "trend_describe": trend_describe,
                "score_range_desc": score_range_desc,
                "top_object_desc": top_object_desc,
                "top_doc_keywords_desc": top_doc_keywords_desc,
                "top_positive_view_keywords_desc": top_positive_view_keywords_desc,
                "top_negative_view_keywords_desc": top_negative_view_keywords_desc
            }


        template = f"""你是一个专业的数据分析师，请从数理角度，帮助用户进行数据分析。用户将提供两份结构相同，但内容不同的数据，分为`左侧数据`和`右侧数据`数据。
请按下面的要求描述两份数据，并且进行对比分析。请确保你的分析和观点基于提供的数据，并且分析过程清晰、观点有一定的深度和实用性。

## 一、数据内容和关注点

**记住以下数据内容，绝不允许数值错误**

<数据量、情感、正面观点数、负面观点数>
`左侧数据`
{res["left"]["trend_df"].to_markdown()}
`右侧数据`
{res["right"]["trend_df"].to_markdown()}

以上表格的描述性统计分析如下：
`左侧数据`
{res["left"]["trend_describe"]}
`右侧数据`
{res["right"]["trend_describe"]}
</数据量、情感、正面观点数、负面观点数>

<情感分布>数据中正面、中性、负面的分布次数
`左侧数据`
{res["left"]["score_range_desc"]}
`右侧数据`
{res["right"]["score_range_desc"]}
</情感分布>

<实体>数据中抽取出的最多重复的实体及次数
`左侧数据`
{res["left"]["top_object_desc"]}
`右侧数据`
{res["right"]["top_object_desc"]}
</实体>

<关键词>数据中抽取出的最高词频关键词及次数
`左侧数据`
{res["left"]["top_doc_keywords_desc"]}
`右侧数据`
{res["right"]["top_doc_keywords_desc"]}
</关键词>

<正面观点关键词>数据中抽取出正面观点中最高频出现的关键词及次数
`左侧数据`
{res["left"]["top_positive_view_keywords_desc"]}
`右侧数据`
{res["right"]["top_positive_view_keywords_desc"]}
</正面观点关键词>

<负面观点关键词>数据中抽取出负面观点中最高频出现的关键词及次数
`左侧数据`
{res["left"]["top_negative_view_keywords_desc"]}
`右侧数据`
{res["right"]["top_negative_view_keywords_desc"]}
</负面观点关键词>

## 二、任务要求

总体要求：**严禁编造**，表述的专业度必须以专业数据分析师为标准。

### 统计分析
对<数据量、情感、正面观点数、负面观点数>进行统计分析，要求如下：
1. 对<数据量、情感、正面观点数、负面观点数>按 `左侧数据`...`右侧数据`... 格式, 逐一说明均值、中位数、偏态/峰态，离群点，及数理论述推断
2. 对<情感分布><实体><关键词><正面观点关键词><负面观点关键词>按 `左侧数据`...`右侧数据`... 格式,做数据分析推断
3. **注意**: 当指出某特定日期数据时，需要完整的说明对应日期

### 数据总结
根据`左侧数据`和`右侧数据`的数据内容，总结对比分析，得出你认为有价值的数据摘要和业务洞察，要求如下：
1. 对以上数据做总结分析和数据推断，之后对左右侧数据的统计结果优劣进行总结对别分析
2. 对0值数据无分析必要时不提及
3. 字数控制在400字左右，输出时不必提示字数多少

### 建议或关注点
根据`左侧数据`和`右侧数据`的数据内容，给出你认为的业务建议或关注点，要求如下：
1. 依次对左侧数据、右侧数据说明建议和关注点，之后对左右侧数据的统计结果进行对比建议和关注点
2. 字数控制在300字左右，输出时不必提示字数多少
3. 只有在对建议和关注点有较强数据支撑的情况下才输出

## 三、输出格式和示例
请按照如下顺序进行输出回复,其中数据说明已经提供，直接复述即可

---
<数据说明>
根据筛选条件
`左侧数据`数据起止时间 {left['start']} 至 {left['end']} 中：已查询到数据总量 {left['total']}，AI分析成功数量 {sum([v['value'] for v in left['sentiment_score_range']])}
`右侧数据`数据起止时间 {right['start']} 至 {right['end']} 中：已查询到数据总量 {right['total']}，AI分析成功数量 {sum([v['value'] for v in right['sentiment_score_range']])}
分析结果如下：

<统计分析>
- **数据量**：`左侧数据`...`右侧数据`...
- **情感**：...
- **正面观点数**：...
- **负面观点数**：...
- **情感分布**：...
- **高频实体**：...
- **高频关键词**：...
- **高频正面观点关键词**：...
- **高频负面观点关键词**：...

<数据总结>
`左侧数据`...
`右侧数据`...
对比分析可知: ...

<建议和关注点>
`左侧数据`...
`右侧数据`...
根据两者的表现：...

---

现在,请完整考虑以上所有要求，给出你的最终回答"""
        return template

    @staticmethod
    async def compare_generator(prompt):
        await TokenCounts.check_plan()

        chat_engine = ChatEngine(LLMModel.QWEN3_30B_INSTRUCT)
        stream_response = await chat_engine.generator(
            system_prompt="You are a helpful assistant.",
            prompt=prompt,
            temperature=0.01,
            stream=True,
        )

        assistant = ""
        async for chunk in stream_response:
            if chunk.choices and chunk.choices[0].delta.content:
                # 逐字符输出内容
                for char in chunk.choices[0].delta.content:
                    assistant += char
                    completion_stream = json.dumps({
                        "stage": "generating",
                        "content": char
                    }, ensure_ascii=False)
                    yield f"data: {completion_stream}\n\n"

        logger.info(f"assistant: {assistant}")

        if chat_engine.token_consumption and g.tenant_id:
            await TokenCounts.record(
                tenant_id=g.tenant_id,
                model_name=LLMModel.QWEN3_30B_INSTRUCT,
                input_tokens=sum(chat_engine.token_consumption.input_token),
                output_tokens=sum(chat_engine.token_consumption.output_token),
                business=LLMBusiness.compare, create_user_id=g.user_id)
            await g.session.commit()

        yield "data: [DONE]\n\n"


Stats = StatsController()
