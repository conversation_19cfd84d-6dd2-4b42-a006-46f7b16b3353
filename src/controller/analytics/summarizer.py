#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import Enum

from sqlalchemy import select, update, func, cast, JSON, case
from taskiq.scheduler.scheduled_task import CronSpec

from config import LLMModel, TZ
from controller.engine import ChatEngine
from engine.es import es_sync
from engine.rdb import g, paginator, query_order, fetch_one, fetch_one_sync, fetch_all, fetch_all_sync
from engine.taskiq_task import broker, redis_schedule_source 
from model.summarizer import SummarizerModel, SummarizerTagModel, SummarizerTaskModel
from model.doc import ALL_REPO_INDEX
from common.logger import logger
from exception import ParamsCheckError


class DataRangePrefix(str, Enum):
    lastday = "lastday"
    keyword = "keyword"
    latest = "latest"


class Execution(str, Enum):
    once = "once"
    crontab = "crontab"


NoTagName = "<无标签>"


class SummarizerController:
    @staticmethod
    def get_query(summarizer_id: int = None, summarizer_ids: list[int] = None, repo_id: int = None,
                  match: str = None, tags: list[str] = None, crontab: bool = None, order_by: str = None,
                  is_delete: int = 0):
        where = []
        if g.tenant_id is not None:
            where.append(SummarizerModel.tenant_id == g.tenant_id)
        if is_delete is not None:
            where.append(SummarizerModel.is_delete == is_delete)
        if summarizer_id is not None:
            where.append(SummarizerModel.id == summarizer_id)
        if summarizer_ids is not None:
            where.append(SummarizerModel.id.in_(summarizer_ids))
        if repo_id is not None:
            where.append(func.json_contains(SummarizerModel.repo_ids, cast(repo_id, JSON)))
        if match:
            where.append(SummarizerModel.name.ilike(f"%{match}%"))
        if tags:
            tag_subquery = (
                select(
                    SummarizerTagModel.summarizer_id)
                .where(
                    SummarizerTagModel.name.in_(tags),
                    SummarizerTagModel.is_delete == 0)
                .group_by(SummarizerTagModel.summarizer_id))
            where.append(SummarizerModel.id.in_(tag_subquery))
        if crontab is True:
            where.append(SummarizerModel.execution != Execution.once)
        if crontab is False:
            where.append(SummarizerModel.execution == Execution.once)

        summarizer_result_subquery = (
            select(
                SummarizerTaskModel.summarizer_id,
                func.max(SummarizerTaskModel.update_time).label("latest_result_time"),
                func.count(SummarizerTaskModel.id).label("result_count"))
            .where(SummarizerTaskModel.is_delete == 0)
            .group_by(SummarizerTaskModel.summarizer_id)
            .subquery())

        query = (
            select(
                SummarizerModel.id.label("summarizer_id"),
                SummarizerModel.tenant_id,
                SummarizerModel.name,
                SummarizerModel.repo_ids,
                SummarizerModel.data_range,
                SummarizerModel.llm_models,
                SummarizerModel.prompt,
                SummarizerModel.execution,
                SummarizerModel.tags,
                SummarizerModel.create_user_id,
                SummarizerModel.create_time,
                SummarizerModel.is_delete,
                summarizer_result_subquery.c.latest_result_time,
                func.coalesce(summarizer_result_subquery.c.result_count, 0).label("result_count"))
            .join(summarizer_result_subquery, SummarizerModel.id == summarizer_result_subquery.c.summarizer_id, isouter=True)
            .where(*where))

        query = query_order(query=query, order_by=order_by, table=SummarizerModel)

        return query

    async def get_list(self, repo_id: int = None, match: str = None, tags: list[str] = None, page: int = 1,
                       per_page: int = 20, order_by: str = None):
        query = self.get_query(repo_id=repo_id, match=match, tags=tags, order_by=order_by)
        pager, summarizers = await paginator(query=query, page=page, per_page=per_page)
        for summarizer in summarizers:
            del summarizer["is_delete"]

        return pager, summarizers

    async def get_all(self, repo_id: int = None, summarizer_ids: list[int] = None, match: str = None,
                      tags: list[str] = None, crontab: bool = None, is_delete: int = 0):
        query = self.get_query(
            repo_id=repo_id, summarizer_ids=summarizer_ids, match=match, tags=tags, crontab=crontab,
            is_delete=is_delete)
        summarizers = await fetch_all(query=query)
        if is_delete == 0:
            for summarizer in summarizers:
                del summarizer["is_delete"]

        return summarizers

    async def get_one(self, summarizer_id: int):
        query = self.get_query(summarizer_id=summarizer_id)
        res = await fetch_one(query=query)
        del res["is_delete"]
        return res

    @staticmethod
    async def get_tags_count():
        query = (
            select(
                SummarizerTagModel.name.label("tag"),
                func.count(SummarizerTagModel.id).label("count")
            )
            .where(SummarizerTagModel.is_delete == 0)
            .group_by(SummarizerTagModel.name)

            .order_by(
                case((SummarizerTagModel.name == NoTagName, 1), else_=0),
                func.count(SummarizerTagModel.id).desc())
            )
        return await fetch_all(query=query)

    async def create(self, name: str, repo_ids: list[int], data_range: str,  llm_models: list[int],
                     prompt: str, execution: str, tags: list[str] = None):
        if tags is None:
            tags = []

        summarizer = SummarizerModel(
            name=name, repo_ids=repo_ids, data_range=data_range, llm_models=llm_models, prompt=prompt, execution=execution,
            tags=tags)
        g.session.add(summarizer)
        await g.session.flush()

        await self.create_tags(summarizer_id=summarizer.id, tags=tags)

        return summarizer.id

    @staticmethod
    async def create_tags(summarizer_id: int, tags: list[str] | None):
        if tags:
            for tag in tags:
                g.session.add(SummarizerTagModel(summarizer_id=summarizer_id, name=tag))
        else:
            g.session.add(SummarizerTagModel(summarizer_id=summarizer_id, name=NoTagName))

    @staticmethod
    async def delete_tags(summarizer_id: int):
        query = (update(SummarizerTagModel)
                 .where(SummarizerTagModel.summarizer_id == summarizer_id)
                 .values({SummarizerTagModel.is_delete: 1}))
        await g.session.execute(query)

    async def create_bulk(self, data_ranges: list[list[str]], repo_ids: list[int], llm_models: list[int],
                          prompt: str, execution: str, tags: list[str] = None):
        for keywords in data_ranges:
            new_tags = keywords[:]
            if tags:
                for t in tags:
                    if t not in new_tags:
                        new_tags.append(t)
            await self.create(
                name="+".join(keywords), repo_ids=repo_ids,
                data_range=f"{DataRangePrefix.keyword}:{','.join(keywords)}", llm_models=llm_models,
                prompt=prompt, execution=execution, tags=new_tags)

    @staticmethod
    def cleat_tags(tags: list[str]):
        if tags is not None:
            new_tags = []
            for t in tags:
                t = t.strip()
                if not t:
                    continue
                if " " in t:
                    raise ParamsCheckError(message=f"标签 [{t}]中禁止出现空格,请修改标签内容")
                if t == NoTagName:
                    raise ParamsCheckError(message=f"请勿使用系统留置词作为标签: {NoTagName}")
                if len(t) > 12:
                    raise ParamsCheckError(message=f"标签 [{t}]长度大于限定长度12,请修改标签内容")
                if t not in new_tags:
                    new_tags.append(t)
            tags = new_tags

        return tags

    @staticmethod
    def clean_data_ranges(data_ranges: str):
        bulk_data_range = []
        for i, keywords in enumerate(data_ranges):
            keywords = keywords.strip()
            if not keywords:
                continue

            data_range_tags = []
            for kw in keywords.split(","):
                k = kw.strip()
                if len(k) > 8:
                    raise ParamsCheckError(message=f"数据范围 第{i + 1}行: [{k}] 长度大于限定长度8,请缩减文本内容")
                if k not in data_range_tags:
                    data_range_tags.append(k)
            if len(data_range_tags) > 3:
                raise ParamsCheckError(message=f"数据范围 第{i+1}行: 不能超过3个有效片段")

            bulk_data_range.append(data_range_tags)

        return bulk_data_range

    async def update(self, summarizer_id: int, name: str = None, repo_ids: list[int] = None, data_range: str = None,
                     llm_models: list[int] = None, prompt: str = None, execution: str = None, tags: list[str] = None):
        update_info = {}
        if name:
            update_info[SummarizerModel.name] = name
        if repo_ids is not None:
            update_info[SummarizerModel.repo_ids] = repo_ids
        if data_range:
            update_info[SummarizerModel.data_range] = data_range
        if llm_models:
            update_info[SummarizerModel.llm_models] = llm_models
        if prompt:
            update_info[SummarizerModel.prompt] = prompt
        if execution:
            update_info[SummarizerModel.execution] = execution
        if tags:
            update_info[SummarizerModel.tags] = tags
            await self.delete_tags(summarizer_id=summarizer_id)
            await self.create_tags(summarizer_id=summarizer_id, tags=tags)

        query = (update(SummarizerModel)
                 .where(SummarizerModel.id == summarizer_id)
                 .values(update_info))
        await g.session.execute(query)

    async def delete(self, summarizer_id: int):
        query = (update(SummarizerModel)
                 .where(SummarizerModel.id == summarizer_id)
                 .values({SummarizerModel.is_delete: 1}))
        await self.delete_tags(summarizer_id=summarizer_id)
        await g.session.execute(query)

    async def send_once_task(self, summarizer_id: int):
        await self.summarizer_abstract.kiq(summarizer_id=summarizer_id)
        logger.info(f"Summarizer[{summarizer_id}]: 单次摘要任务已发送")

    async def send_crontab_task(self, summarizer_id: int, crontab: str):
        cs = crontab.split(" ")
        schedule = (
            await self.summarizer_abstract.kicker()
            .with_schedule_id(self.get_abstract_schedule_task_id(summarizer_id))
            .schedule_by_cron(
                redis_schedule_source,
                cron=CronSpec(minutes=cs[0], hours=cs[1], days=cs[2], months=cs[3], weekdays=cs[4], offset=TZ.zone),
                summarizer_id=summarizer_id
            )
        )
        logger.info(f"Summarizer[{summarizer_id}]: 定时摘要任务已发送 {schedule.schedule_id}")

    async def delete_crontab_task(self, summarizer_id: int):
        schedule_id = self.get_abstract_schedule_task_id(summarizer_id)
        await redis_schedule_source.delete_schedule(schedule_id)
        logger.info(f"Summarizer[{summarizer_id}]: 定时摘要任务已删除 {schedule_id}")

    @staticmethod
    def get_abstract_schedule_task_id(summarizer_id: int):
        return f"summarizer_abstract_{summarizer_id}"

    # tasks.summarizer_abstract的占位方法
    @staticmethod
    @broker.task(task_name="summarizer_abstract")
    async def summarizer_abstract(summarizer_id: int): ...


Summarizer = SummarizerController()
