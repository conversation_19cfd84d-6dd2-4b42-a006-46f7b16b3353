#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime
from enum import StrEnum, IntEnum

from sqlalchemy import select, update
from pydantic import BaseModel, Field

from model.analytics import AnalyticsTaskModel
from model.analytics_metrics_es import METRICS_INDEX
from engine.rdb import g, session_maker_sync, query_order, fetch_all
from engine.es import es
from common.time import now_datetime_str


class MetricShowType(StrEnum):
    default = "default"
    metric = "metric"


class MetricResult(BaseModel):
    metric_id: int = Field(title="指标结果表")
    value: int | float | str | None = Field(title="指标值")
    explanation: str | None = Field(title="分析解释")


class AnalyticsTaskStatus(IntEnum):
    failed = -1
    created = 0
    running = 1
    partially_succeeded = 2
    succeeded = 3


class AnalyticsMetricsController:
    @staticmethod
    def get_task_query(dashboard_id: int = None, max_task_id: int = None, status: int = None, depth: int = None,
                       order_by: str = None):
        where = [AnalyticsTaskModel.is_delete == 0]
        if dashboard_id is not None:
            where.append(AnalyticsTaskModel.dashboard_id == dashboard_id)
        if max_task_id is not None:
            where.append(AnalyticsTaskModel.id <= max_task_id)
        if status is not None:
            where.append(AnalyticsTaskModel.status == status)

        query = (
            select(
                AnalyticsTaskModel.id.label("task_id"),
                AnalyticsTaskModel.data_time)
            .where(*where))

        query = query_order(query=query, table=AnalyticsTaskModel, order_by=order_by)
        if depth is not None:
            query = query.limit(depth)

        return query

    async def get_task_all(self, dashboard_id: int = None, max_task_id: int = None, status: int = None,
                           depth: int = None, order_by: str = None):
        query = self.get_task_query(
            dashboard_id=dashboard_id, max_task_id=max_task_id, status=status, depth=depth, order_by=order_by)
        return await fetch_all(query=query)

    @staticmethod
    async def get_metrics_data(dashboard_id: int, task_ids: list[int], item_ids: list[int] = None, desc: bool = True):
        filters = [
            {"term": {"dashboard_id": dashboard_id}},
            {"terms": {"task_id": task_ids}},
        ]

        if item_ids is not None:
            filters.append({"terms": {"item_id": item_ids}})

        res = await es.search(
            index=METRICS_INDEX,
            query={
                "bool": {
                    "filter": filters
                }
            },
            collapse={
                "field": "item_id",
                "inner_hits": {
                    "name": "items",
                    "size": len(task_ids),
                    "sort": [
                        {"task_id": {"order": "desc" if desc else "asc"}}
                    ],
                    "_source": {
                        "includes": ["extract_metrics", "data_time", "task_id"],
                    }
                }
            },
            source_excludes=["*"],
            size=10000)
        metric_data = [{
            "item_id": hit["fields"]["item_id"][0],
            "metrics": [{
                "task_id": metric["_source"]["task_id"],
                "extract_metrics": metric["_source"]["extract_metrics"],
                "data_time": metric["_source"]["data_time"]} for metric in hit["inner_hits"]["items"]["hits"]["hits"]]}
            for hit in res["hits"]["hits"]]

        return metric_data

    @staticmethod
    async def get_source(task_id: int, item_id: int):
        res = await es.search(
            index=METRICS_INDEX,
            query={
                "bool": {
                    "filter": [
                        {"term": {"task_id": task_id}},
                        {"term": {"item_id": item_id}},
                    ]
                }
            },
            source_includes=["source_content", "create_time"],
            size=1)
        hits = res["hits"]["hits"]
        if not hits:
            return None

        source = hits[0]["_source"]
        return source

    @staticmethod
    async def get_last_item_success_result(item_id: int):
        res = await es.search(
            index=METRICS_INDEX,
            query={
                "bool": {
                    "filter": [
                        {"term": {"item_id": item_id}},
                        {"term": {"status": AnalyticsTaskStatus.succeeded.value}},
                    ]
                }
            },
            sort=[{"task_id": {"order": "desc"}}],
            source_excludes=["create_time"],
            size=1
        )
        hits = res["hits"]["hits"]
        if not hits:
            return None

        source = hits[0]["_source"]
        return source

    @staticmethod
    async def create_metric(dashboard_id: int, task_id: int, item_id: int, summarizer_task_ids: list[int],
                            source_content: str, extract_metrics: list[MetricResult] | list[dict], data_time: str,
                            status: int, input_tokens: int = None, output_tokens: int = None, copy_to: int = None):
        res = await es.index(
            index=METRICS_INDEX,
            id=f"{dashboard_id}_{task_id}_{item_id}",
            document={
                "dashboard_id": dashboard_id,
                "task_id": task_id,
                "item_id": item_id,
                "status": status,
                "summarizer_task_ids": summarizer_task_ids,
                "source_content": source_content,
                "extract_metrics": [em if isinstance(em, dict) else em.model_dump() for em in extract_metrics],
                "data_time": data_time,
                "copy_to": copy_to,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "create_time": now_datetime_str()
            })

    @staticmethod
    async def create_task(dashboard_id: int, data_time: str | datetime.datetime):
        analytics_task = AnalyticsTaskModel(
            dashboard_id=dashboard_id, data_time=data_time, status=AnalyticsTaskStatus.created)
        g.session.add(analytics_task)

        await g.session.flush()
        return analytics_task.id

    @staticmethod
    async def update_task(dashboard_id: int, status: int = None):
        update_info = {}
        if status is not None:
            update_info[AnalyticsTaskModel.status] = status

        query = (update(AnalyticsTaskModel)
                 .where(AnalyticsTaskModel.dashboard_id == dashboard_id)
                 .values(update_info))
        await g.session.execute(query)




Metrics = AnalyticsMetricsController()
