from datetime import datetime

from fastapi import Request, FastAPI
from fastapi.exceptions import RequestValidationError, ResponseValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError

from common import g
from common.logger import logger as logging
from exception import ApiError, UnknownErrorCode, ParamCheckErrorCode, MESSAGE, status


def register_exception_handler(app: FastAPI):
    def human_errors(exc: ValidationError) -> str:
        errors = exc.errors()
        for error in errors:
            loc = list(error.get("loc", []))
            loc_len = len(loc)
            msg = error.get("msg")
            error_type = error.get("type")
            if error_type == "json_invalid":
                return msg
            if loc_len == 1:
                text = f"Field {loc[0]} error: {msg}"
                return text
            if loc_len == 2:
                text = "Field "
                if isinstance(loc[1], int):
                    text += f"{loc[0]}[{loc[1]}]"
                if isinstance(loc[1], str):
                    text += f"`{loc[1]}`"
                text += " error: " + msg
                return text
            if loc_len > 2:
                key_field = None
                if loc[-1] == "[key]":
                    key_field = loc.pop()
                text = "Field "
                for ix, field in enumerate(loc[1:]):
                    if isinstance(field, str):
                        if ix == 0:
                            text += field
                        else:
                            text += f"['{field}']"
                    if isinstance(field, int):
                        text += f"[{field}]"
                if key_field:
                    text += f" the value '{loc[-1]}'"
                    text += " " + msg
                else:
                    text += " error: " + msg
                return text
        return str(errors)

    @app.exception_handler(ApiError)
    async def custom_exception_handler(request: Request, exc: ApiError):
        return JSONResponse(
            status_code=exc.http_code,
            content={"message": f"{exc.message}", "code": exc.code, "trace_id":  g.trace_id},
        )

    @app.exception_handler(Exception)
    async def exception_handler(request: Request, exc):
        code = UnknownErrorCode
        return JSONResponse(
            status_code=MESSAGE[code]["http_code"],
            content={"message": f"{exc}", "code": code, "trace_id":  g.trace_id},
        )

    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request, exc):
        try:
            human_err = human_errors(exc)
        except Exception:
            logging.warning(f"format error fail:")
            return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST,
                                content={"message": str(exc), "code": ParamCheckErrorCode})
        return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST,
                            content={"message": human_err, "code": ParamCheckErrorCode})

    @app.exception_handler(ValidationError)
    async def pydantic_validation_handler(request, exc):
        try:
            human_err = human_errors(exc)
        except Exception:
            logging.warning(f"format error fail:")
            return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST,
                                content={"message": str(exc), "code": ParamCheckErrorCode})
        return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST,
                            content={"message": human_err, "code": ParamCheckErrorCode})

    @app.exception_handler(ResponseValidationError)
    async def response_exception_handler(request, exc):
        try:
            human_err = human_errors(exc)
        except Exception:
            logging.warning(f"format error fail:")
            return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST,
                                content={"message": str(exc), "code": ParamCheckErrorCode})
        return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST,
                            content={"message": human_err, "code": ParamCheckErrorCode})

    @app.middleware("http")
    async def common_requests(request: Request, call_next):
        async def get_request_body():
            try:
                request_body = await request.json()
                if "embedding" in request_body:
                    request_body["embedding"] = ["..."]
            except:
                return {}
            return request_body

        # 记录请求开始时间
        start_time = datetime.now()
        # 获取请求信息
        method = request.method
        url = str(request.url.path)
        client_ip = request.client.host
        client_agent = request.headers.get("user-agent")
        query_params = dict(request.query_params)
        request_body = await get_request_body()
        g.request = request
        g.extra_data = {}
        # 处理请求
        try:
            response = await call_next(request)
        except Exception:
            logging.exception(
                f"接口异常: {method}: {url}, 用时: {(datetime.now() - start_time).total_seconds():.4f}, "
                f"Query Params: {query_params}, "
                f"Body: {request_body} IP: {client_ip}, Agent: {client_agent}")
            raise
        finally:
            logging.info(f"HTTP Request {method}: {url}, Query Params: {query_params}, "
                        f"Body: {request_body} IP: {client_ip}, Agent: {client_agent}, "
                        f"Start Time: {start_time}, "
                        f"用时: {(datetime.now() - start_time).total_seconds():.4f}")
        return response
