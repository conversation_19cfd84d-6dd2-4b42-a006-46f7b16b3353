#!/usr/bin/env python3
# -*- coding: utf-8 -*-
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import time
from enum import StrEnum

from pathlib import Path

import httpx
from lxml import html as lxmlhtml

f = Path(r"C:\Users\<USER>\Downloads\交通银行业务人员产品学习参考手册 (2).pdf")

s = time.time()
parameters = {
    "return_middle_json": True,
    "return_model_output": False,
    "return_md": True,
    "return_images": True,
    "parse_method": "ocr",
    "backend": "pipeline",
    "table_enable": True,
    "return_content_list": False,
    "formula_enable": True,
    # "start_page_id": 19,
    # "end_page_id": 22
}
resp = httpx.post(
    url="http://bchampion.cn:11016/file_parse",
    data=parameters,
    files={
        "files": (f.name, open(f.as_posix(), mode="rb"))
    },
    timeout=300
)
resp_json = resp.json()
print(time.time() - s)
# print(resp_json)

file_result = resp_json["results"][f.stem]
md_content = file_result["md_content"]
images = file_result["images"]
paragraphs = md_content.split("\n")


class MinerUType(StrEnum):
    title = "title"
    text = "text"
    list = "list"
    image = "image"
    table = "table"
    formula = "formula"
    interline_equation = "interline_equation"


def storage_text(block: dict, full_line_radio: float = 0.7):
    text = ""
    block_bbox = block["bbox"]
    for i, line in enumerate(block["lines"]):
        spans = []
        for span in line["spans"]:
            if span.get("content"):
                spans.append(span["content"])
        text += " ".join(spans)

        if line.get("is_list_end_line"):
            text += " \n "  # 特殊标记
        if (
                text  # 已有文本
                and i != 0  # 索引不是第一位
                and text[-1] != " "
                and (
                        block["lines"][i-1]["bbox"][2] < block_bbox[2] * full_line_radio  # 上一行x1不足满行 full_line_radio%
                        or (
                                text[-1].isalpha() or ord(text[-1]) < 128  # 英文字母
                                or text[-1].isdigit()  # 或数字
                        )
                )):
            # 极大概率是换行, 增加换行符
            text += " "
    return text.strip()


def build_page_box(p: int, pages_size: dict, block: dict):
    # 2.1.1版本,para_blocks的bbox和其下lines.bbox坐标不对等.
    # 为了处理跨页问题,以lines.bbox为准
    boxes = []
    # cross_page标记只能管翻过一页,但是不管跨两页.
    # 使用标记强制翻第一页,根据bbox有选择的翻第二页
    cross_page_flag = False

    if block.get("blocks"):
        block.setdefault("lines", [])
        block["lines"].extend([line for b in block["blocks"] for line in b["lines"]])

    for line in block["lines"]:
        if not boxes:
            boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
            continue

        # layout判断跨超过一页的情况.需要cross_page不在本次出现,并且bbox有大幅变动
        if (cross_page_flag is True
                and abs(line["bbox"][1] - boxes[-1]["bbox"][3]) > 30  # y0小于上个bbox y1 20个像素以上
                and line["bbox"][0] < boxes[-1]["bbox"][2]  # x0不大于上个bbox的x1
            ):
            p += 1
            boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
            continue

        # 首次出现cross_page 强制翻页
        if line["spans"] and line["spans"][0].get("cross_page") and cross_page_flag is False:
            cross_page_flag = True
            p += 1
            boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
            continue

        # 块产生较大变动,重新分块
        if line["bbox"][0] > boxes[-1]["bbox"][2] or abs(line["bbox"][1] - boxes[-1]["bbox"][3]) > 30:
            boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
            continue

        # 未检测到的其他情况,扩展bbox
        boxes[-1]["bbox"][0] = min(boxes[-1]["bbox"][0], line["bbox"][0])
        boxes[-1]["bbox"][1] = min(boxes[-1]["bbox"][1], line["bbox"][1])
        boxes[-1]["bbox"][2] = max(boxes[-1]["bbox"][2], line["bbox"][2])
        boxes[-1]["bbox"][3] = max(boxes[-1]["bbox"][3], line["bbox"][3])

    return boxes


doc_htmls = []
doc_plain_texts = []
middle_json = json.loads(file_result["middle_json"])
pages_size = {p["page_idx"] + 1: p["page_size"] for p in middle_json["pdf_info"]}
for page in middle_json["pdf_info"]:
    for block in page["para_blocks"]:
        type_ = block["type"]
        p = page["page_idx"] + 1
        bboxes = build_page_box(p=p, pages_size=pages_size, block=block)

        if type_ == MinerUType.image:
            plain_text = ""
            image_caption = []
            img_tags = []
            for b in block["blocks"]:
                # 图片本体,构造html标签
                if b["type"] == "image_body":
                    for line in b["lines"]:
                        for span in line["spans"]:
                            if span.get("image_path"):
                                img_tags.append(f'<img src="{images}">')
                # 图片描述
                if b["type"] == "image_caption":
                    text = storage_text(b)
                    plain_text += f"{text} "
                    image_caption.append(f"<span>{text}</span>")
            # 最后,如有多个image_caption,组合为figcaption
            html_content = (f"<figcaption>"
                            f"{''.join(img_tags)}"
                            f"{'<br>'.join(image_caption)}"
                            f"</figcaption>")

        elif type_ == MinerUType.table:
            plain_text = ""
            table_caption = []
            table_body = ""
            for b in block["blocks"]:
                # 表格本体,构造html标签
                if b["type"] == "table_body":
                    if table_body:
                        print("MinerU解析同para下出现多个表格")
                    for line in b["lines"]:
                        for span in line["spans"]:
                            table_body = span["html"][19:-22]
                            plain_text = lxmlhtml.fromstring(table_body).text_content()


                # 表格描述
                if b["type"] == "table_caption":
                    text = storage_text(b)
                    plain_text += f"{text} "
                    table_caption.append(text)

            html_content = "<table>" + "\n".join(table_caption) + table_body + "</table>"

        elif type_ == MinerUType.title:
            text = storage_text(block)
            plain_text = text
            if len(doc_htmls) < 2:
                html_content = f"<h1>{text}</h1>"
            else:
                html_content = f"<h2>{text}</h2>"

        elif type_ == MinerUType.list:
            text = storage_text(block)
            plain_text = text.replace(" \n ", " ")
            li_tags = [f"<li>{text}</li>" for text in text.split(" \n ")]
            html_content = f"<p>{''.join(li_tags)}</p>"

        elif type_ == MinerUType.text:
            text = storage_text(block)
            plain_text = text
            html_content = f"<p>{text}</p>"

        elif type_ == MinerUType.interline_equation:
            equations = []
            html_content = ""
            for line in block["lines"]:
                for span in line["spans"]:
                    equations.append(span["content"])
                    html_content += f'<p><img src="{images[span["image_path"]]}"></p>'
            plain_text = " \n ".join([f"$${eq}$$" for eq in equations])
            print(plain_text)
        else:
            print(f"有未知的MinerU paragraph <{type_}>")

            text = storage_text(block)
            plain_text = f"$${text}$$"
            html_content = f'<p><img src="{images}"></p>'
            continue

        if not bboxes:
            continue
        doc_htmls.append({
            "html": html_content,
            "plain_text": plain_text,
            "bboxes": bboxes
        })


print(doc_htmls)

