# LLM_DA 项目文档

## 文档索引

- [代码风格与设计模式分析](code_style_patterns.md)

## 项目概述

LLM_DA是一个基于大型语言模型的数据分析平台，集成了自由问答、文档管理、数据分析、摘要生成等功能。本项目采用Python技术栈开发，使用FastAPI作为Web框架，支持异步处理和流式响应。

## 主要功能

- **自由问答**：基于大型语言模型的对话功能
- **文档管理**：文档上传、检索和管理
- **摘要生成**：自动为文档生成摘要
- **数据分析**：数据可视化和分析功能
- **权限管理**：用户角色和权限控制

## 技术栈

- FastAPI
- SQLAlchemy
- Elasticsearch
- Redis
- Celery与Taskiq
- Python 3.10+

## 文档导航

请查看侧边栏或上方的文档索引，了解更多项目细节。 